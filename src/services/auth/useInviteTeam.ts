import apiClient from '@/lib/api-client';
import { handleApiError } from '@/lib/error-handler';
import { useMutation } from '@tanstack/react-query';
import { InviteTeamPayload } from '@/types/inviteTeam.types';
import { AxiosError } from 'axios';
import { UseMutationHookProps } from '@/types/api';
import { apiUrls } from '@/constants/apiUrls';
import { UserRole } from '@/types/user.types';

export async function postInviteTeam(
  userRole: UserRole,
  payload: InviteTeamPayload,
): Promise<void> {
  try {
    await apiClient.post(apiUrls.users.invite(userRole), payload);
  } catch (error) {
    throw handleApiError(error, 'Failed to send invites');
  }
}

export const usePostInviteTeam = ({
  onSuccess,
  onError,
}: UseMutationHookProps<void, { message: string }>) => {
  return useMutation<
    void,
    AxiosError<{ message: string }>,
    { userRole: UserRole; payload: InviteTeamPayload }
  >({
    mutationFn: ({ userRole, payload }) => postInviteTeam(userRole, payload),
    onSuccess,
    onError,
  });
};
