import apiClient from '@/lib/api-client';
import { handleApiError } from '@/lib/error-handler';
import { SourcingIntentPayload } from '@/types/sourcingItent.types';
import { useMutation, useQueryClient } from '@tanstack/react-query';

async function updateSourcingIntent(
  userRole: string,
  payload: SourcingIntentPayload,
): Promise<any> {
  try {
    const response = await apiClient.patch<any>(
      `/api/v1/users/${userRole}/sourcing-intent/update/`,
      payload,
    );
    return response.data!;
  } catch (error) {
    throw handleApiError(error, 'Failed to update sourcing intent.');
  }
}

export const useUpdateSourcingIntent = (userRole: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: SourcingIntentPayload) => updateSourcingIntent(userRole, payload),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['buyer', 'sourcing-intent', 'info'],
      });
    },
  });
};
