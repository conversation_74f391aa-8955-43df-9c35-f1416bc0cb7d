import { InvitedTeamResponse } from '@/components/buyer/team-members-management/invite-team/types';
import { apiUrls } from '@/constants/apiUrls';
import axiosInstance from '@/lib/api-client';
import { useQuery } from '@tanstack/react-query';

const invitedTeamMembers = async () => {
  const res = await axiosInstance.get<InvitedTeamResponse>(apiUrls.users.invitedTeamMembers);
  return res.data;
};

export const useGetInvitedTeamMembers = (userType: 'buyer' | 'supplier') => {
  return useQuery({
    queryFn: invitedTeamMembers,
    queryKey: ['invited-team-members', userType],
  });
};
