import { useGetUserInfo } from '@/services/auth/useGetUserInfo';
import { useGetKycStatus } from '@/services/kyc/useGetKycStatus';
import { getKycStatus, kycFormKeyMap } from '@/utils/kyc';
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  Badge,
} from '@kratex-tradetech/kratex-ui-library';
import { CheckCircle, Loader2 } from 'lucide-react';
import { useEffect, useMemo } from 'react';
import { toast } from 'sonner';

const KycStatus = () => {
  const { data: userData } = useGetUserInfo('buyer');
  const { data, isLoading, isError, isSuccess } = useGetKycStatus({
    keyword: userData?.organisation_type?.id ? kycFormKeyMap[userData?.organisation_type?.id] : '',
    userType: 'buyer',
  });

  const kycStatus = useMemo(
    () =>
      isSuccess && data
        ? getKycStatus(data?.status)
        : 'Error fetching KYC status. Please try again later.',
    [isSuccess, data],
  );

  useEffect(() => {
    if (isError) {
      toast.error('Error fetching KYC status! Please try again later.');
    }
  }, [isError]);

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-lg">
          <CheckCircle className="h-4 w-4" />
          KYC Status
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex items-center gap-2">
          {isLoading ? (
            <Loader2 className="animate-spin" />
          ) : (
            <>
              <Badge variant={kycStatus === 'Active' ? 'default' : 'secondary'}>{kycStatus}</Badge>
              <p className="text-sm text-gray-600">
                {data?.status
                  ? kycStatus === 'Active'
                    ? 'Your account will be activated immediately'
                    : 'Your account will be pending KYC verification'
                  : ''}
              </p>
            </>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default KycStatus;
