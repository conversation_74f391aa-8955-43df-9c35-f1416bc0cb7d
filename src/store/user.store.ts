import { User } from '@/types/user.types';
import { createPersistedStore } from '@/utils/zustand/createPersistedStore';

// Define default user values
const defaultUser: User = {
  role: 0,
  GivenName: '',
  Surname: '',
  Email: '',
  isRegistered: false,
};

type UserState = {
  user: User;
  setUser: (user: User) => void;
  clearUser: () => void;
};

export const userStore = createPersistedStore<UserState>('user-store', (set) => ({
  user: defaultUser,
  setUser: (user: User) => set({ user }),
  clearUser: () => set({ user: defaultUser }), // Reset to default instead of null
}));
