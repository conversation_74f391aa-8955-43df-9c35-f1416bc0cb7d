'use client';
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { useEffect, useState, useRef, useCallback } from 'react';
import type { UseFormReturn } from 'react-hook-form';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
  Card,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Input,
  Textarea,
  Checkbox,
  InFormEmailVerification,
  Calendar,
  RadioGroup,
  RadioGroupItem,
  Popover,
  PopoverTrigger,
  Button,
  PopoverContent,
} from '@kratex-tradetech/kratex-ui-library';
import type { FormioComponent } from './types';
import { CheckCircle, ChevronDownIcon, Mail } from 'lucide-react';
import { newBuyerStore } from '@/store/newBuyer.store';
import { toast } from 'sonner';
import FileUpload from '@/components/reusable/file-upload/FileUpload';
import { renderCustomModule } from './renderCustomModule';
import { useVerifyEmailOTP } from '@/services/auth/useVerifyEmailOTP';
import { useRequestEmailOTP } from '@/services/auth/useRequestEmailOTP';
import { AxiosError } from 'axios';

interface FormioComponentRendererProps {
  component: FormioComponent;
  form: UseFormReturn<any>;
  formSubmitSuccessCallback?: () => void;
  formSubmitErrorCallback?: () => void;
}

export const FormioComponentRenderer: React.FC<FormioComponentRendererProps> = ({
  component,
  form,
  formSubmitSuccessCallback,
  formSubmitErrorCallback,
}) => {
  const { newBuyerData, setEmailVerified } = newBuyerStore();
  const currentEmail = newBuyerData?.accountBasics?.email || '';
  const [localEmail, setLocalEmail] = useState(currentEmail);
  const [flowId, setFlowId] = useState<string>('');
  const [open, setOpen] = useState(false);

  const requestEmailOTPMutation = useRequestEmailOTP({
    onError: (error: AxiosError<{ message: string }>) => {
      toast.error(error.message || 'Failed to send OTP');
    },
    onSuccess: () => {
      toast.success('OTP sent to your email');
    },
  });
  const verifyEmailOTPMutation = useVerifyEmailOTP();

  // Track previous visibility state to prevent unnecessary unregisters
  const prevIsVisible = useRef<boolean>(false);
  const hasUnregistered = useRef<boolean>(false);

  const { conditional, inputMask = 'a' } = component;
  const { unregister } = form;

  // 1. PERFORMANCE FIX: Watch only the dependent field, not the whole form.
  const watchedValue = conditional?.when ? form.watch(conditional.when) : undefined;

  let isVisible = true;
  if (conditional?.when) {
    const conditionMet = watchedValue === conditional.eq;
    isVisible = conditional.show === undefined || conditional.show ? conditionMet : !conditionMet;
  }

  const stableUnregister = useCallback(
    (key: string) => {
      if (!hasUnregistered.current) {
        unregister(key);
        hasUnregistered.current = true;
      }
    },
    [unregister],
  );

  // Only unregister when visibility actually changes from true to false
  useEffect(() => {
    if (
      prevIsVisible.current !== undefined &&
      prevIsVisible.current === true &&
      isVisible === false &&
      component.key
    ) {
      stableUnregister(component.key);
    }

    // Reset unregister flag when component becomes visible again
    if (isVisible && hasUnregistered.current) {
      hasUnregistered.current = false;
    }

    prevIsVisible.current = isVisible;
  }, [isVisible, component.key, stableUnregister]);

  // If the component should not be visible, render nothing.
  if (!isVisible) {
    return null;
  }

  const renderInput = () => {
    const options =
      component.data?.values ??
      (component.values as Array<{ label: string; value: string }> | undefined) ??
      [];

    switch (component.type) {
      case 'textfield':
        if (component?.properties?.key === 'phone')
          return renderCustomModule(component.key, form, component.disabled);
        return (
          <FormField
            control={form.control}
            name={component.key!}
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  {component.label}
                  {component.validate?.required ? <span style={{ color: 'red' }}> *</span> : null}
                </FormLabel>
                <FormControl>
                  <Input
                    title={component?.description}
                    placeholder={component.placeholder}
                    className={component.customClass}
                    {...field}
                    type={inputMask === '9' ? 'number' : 'text'}
                    value={field.value || ''}
                  />
                </FormControl>
                <span className="text-xs">{component?.description}</span>
                <FormMessage />
              </FormItem>
            )}
          />
        );

      case 'textarea':
        return (
          <FormField
            control={form.control}
            name={component.key!}
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  {component.label}
                  {component.validate?.required ? <span style={{ color: 'red' }}> *</span> : null}
                </FormLabel>
                <FormControl>
                  <Textarea
                    placeholder={component.placeholder}
                    className={component.customClass}
                    {...field}
                    value={field.value || ''}
                  />
                </FormControl>
                <FormMessage aria-errormessage={component.validate?.requiredMessage} />
              </FormItem>
            )}
          />
        );

      case 'button':
        return (
          <FormField
            control={form.control}
            name={component.key!}
            render={({ field }) => (
              <FormItem>
                <Button
                  type="button"
                  className={component.customClass}
                  onClick={(e) => {
                    e.preventDefault();
                    // loading the custom event handler if the button action is event in formio
                    if (component.key === 'submit') {
                      // Check if this is a submit button
                      //Note: if a form has a submit button, its key should be 'submit', lable can be anything
                      // For submit button: run form validation first
                      form.handleSubmit((validData) => {
                        // After validation passes, run the custom script
                        if (component.action === 'custom' && component.custom) {
                          const formContext = {
                            getComponent: (key: string) => ({
                              getValue: () => form.getValues()[key],
                              setValue: (value: any) => form.setValue(key, value),
                            }),
                            submission: { data: validData },
                            setValue: (obj: { data: any }) => {
                              Object.entries(obj.data).forEach(([key, value]) => {
                                form.setValue(key, value);
                              });
                            },
                            triggerAppCallback: (payload: {
                              type: 'success' | 'error';
                              message: string;
                              metaData: any;
                            }) => {
                              if (payload.type === 'success') {
                                formSubmitSuccessCallback?.();
                              } else {
                                formSubmitErrorCallback?.();
                              }
                            },
                          };

                          try {
                            const customFn = new Function('form', component.custom);
                            customFn(formContext);
                          } catch (err: any) {
                            console.error('Submit button error:', err);
                            toast.error('Submit button action failed: ' + err.message);
                          }
                        }
                      })(e);
                    } else {
                      // For non-submit buttons: run custom logic directly without validation
                      if (component.action === 'custom' && component.custom) {
                        const formContext = {
                          getComponent: (key: string) => ({
                            getValue: () => form.getValues()[key],
                            setValue: (value: any) => form.setValue(key, value),
                          }),
                          submission: { data: form.getValues() }, // Get current form values without validation
                          setValue: (obj: { data: any }) => {
                            Object.entries(obj.data).forEach(([key, value]) => {
                              form.setValue(key, value);
                            });
                          },
                          // Note: No triggerAppCallback for non-submit buttons
                          // or you can provide a different callback if needed
                        };

                        try {
                          const customFn = new Function('form', component.custom);
                          customFn(formContext);
                        } catch (err: any) {
                          console.error('Button action error:', err);
                          toast.error('Button action failed: ' + err.message);
                        }
                      }
                    }
                  }}
                >
                  {component.label}
                </Button>
              </FormItem>
            )}
          />
        );

      case 'email':
        if (newBuyerData?.isEmailVerified) {
          return (
            <FormField
              control={form.control}
              name={component.key!}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{component.label}</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Mail className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform text-gray-400" />
                      <Input
                        type="email"
                        placeholder={component.placeholder}
                        {...field}
                        value={newBuyerData?.accountBasics?.email || field.value || ''}
                        readOnly
                        className={`cursor-not-allowed bg-gray-50 pl-10 text-gray-500 ${component.customClass}`}
                      />
                      <CheckCircle className="absolute top-1/2 right-3 h-4 w-4 -translate-y-1/2 transform text-green-500" />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          );
        } else {
          return (
            <InFormEmailVerification
              email={localEmail}
              className={component.customClass}
              onEmailChange={(email: string) => {
                setLocalEmail(email);
                form.setValue(component.key!, email);
              }}
              required={component.validate?.required}
              onVerificationComplete={(data: any) => {
                setEmailVerified(true);
                toast.success('Email verified successfully!');
              }}
              onVerificationStart={(data: any) => {
                // console.log('Email verification started:', data, newBuyerData.isEmailVerified);
              }}
              sendOTPFunction={async () => {
                if (!localEmail && component.validate?.required) {
                  return { success: false, message: 'Email is required' };
                }

                try {
                  const response = await requestEmailOTPMutation.mutateAsync({
                    email: localEmail,
                  } as { email: string });
                  setFlowId(response.flow);
                  return {
                    success: true,
                    message: 'OTP sent successfully',
                  };
                } catch (error: any) {
                  return {
                    success: false,
                    message: error?.message || 'Failed to send OTP',
                  };
                }
              }}
              verifyOTPFunction={async (email: string, otp: string) => {
                if (!email || !otp || !flowId) {
                  return {
                    success: false,
                    message: 'Email, OTP, and flow ID are required',
                  };
                }

                try {
                  const response = await verifyEmailOTPMutation.mutateAsync({
                    email,
                    otp,
                    flow_id: flowId,
                  });
                  return {
                    success: true,
                    message: response.message || 'OTP verified successfully',
                  };
                } catch (error: any) {
                  return {
                    success: false,
                    message: error.response?.data?.message || 'Failed to verify OTP',
                  };
                }
              }}
              style="default"
            />
          );
        }

      case 'number':
        return (
          <FormField
            control={form.control}
            name={component.key!}
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  {component.label}
                  {component.validate?.required ? <span style={{ color: 'red' }}> *</span> : null}
                </FormLabel>
                <FormControl>
                  <Input
                    className={component.customClass}
                    type="number"
                    placeholder={component.placeholder}
                    {...field}
                    value={field.value || ''}
                    onChange={(e) => field.onChange(Number(e.target.value))}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        );

      case 'select':
        return (
          <>
            <FormField
              control={form.control}
              name={component.key!}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {component.label}
                    {component.validate?.required ? <span style={{ color: 'red' }}> *</span> : null}
                  </FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    value={field.value || component.defaultValue}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue
                          className={component.customClass}
                          placeholder={component.placeholder}
                        />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {component.data?.values
                        ?.filter((option) => option.value !== '')
                        .map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            {component.properties?.customRender &&
              renderCustomModule(component.key, form, component.disabled)}
          </>
        );

      case 'radio':
        return (
          <FormField
            control={form.control}
            name={component.key!}
            render={({ field }) => (
              <FormItem className="space-y-3">
                <FormLabel>
                  {component.label}
                  {component.validate?.required ? <span style={{ color: 'red' }}> *</span> : null}
                </FormLabel>
                <FormControl>
                  <RadioGroup
                    onValueChange={field.onChange}
                    value={field.value}
                    className={`flex flex-col space-y-1 ${component.customClass}`}
                  >
                    {options.map((opt) => (
                      <div key={opt.value} className="flex items-center space-x-2">
                        <RadioGroupItem value={opt.value} id={`${component.key}-${opt.value}`} />
                        <label htmlFor={`${component.key}-${opt.value}`} className="text-sm">
                          {opt.label}
                        </label>
                      </div>
                    ))}
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        );

      case 'checkbox':
        if (options.length > 0) {
          return (
            <FormField
              control={form.control}
              name={component.key!}
              render={({ field }) => {
                const selected: string[] = Array.isArray(field.value) ? field.value : [];
                const toggle = (val: string) => {
                  const next = selected.includes(val)
                    ? selected.filter((v) => v !== val)
                    : [...selected, val];
                  field.onChange(next);
                };

                return (
                  <FormItem className="space-y-2">
                    <FormLabel>
                      {component.label}
                      {component.validate?.required ? (
                        <span style={{ color: 'red' }}> *</span>
                      ) : null}
                    </FormLabel>
                    <FormControl>
                      <div className="flex flex-col space-y-1">
                        {options.map((opt) => (
                          <div key={opt.value} className="flex items-center space-x-2">
                            <Checkbox
                              checked={selected.includes(opt.value)}
                              className={component.customClass}
                              onCheckedChange={() => toggle(opt.value)}
                              id={`${component.key}-${opt.value}`}
                            />
                            <label htmlFor={`${component.key}-${opt.value}`} className="text-sm">
                              {opt.label}
                            </label>
                          </div>
                        ))}
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                );
              }}
            />
          );
        }

        return (
          <FormField
            control={form.control}
            name={component.key!}
            render={({ field }) => (
              <FormItem className="flex items-start space-x-2">
                <FormControl>
                  <Checkbox
                    id={component.key}
                    className={component.customClass}
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
                <div>
                  <FormLabel htmlFor={component.key}>{component.label}</FormLabel>
                  {component.description && (
                    <FormDescription>{component.description}</FormDescription>
                  )}
                  <FormMessage />
                </div>
              </FormItem>
            )}
          />
        );

      case 'datetime':
        return (
          <FormField
            control={form.control}
            name={component.key!}
            render={({ field }) => {
              return (
                <FormItem>
                  <FormLabel>
                    {component.label}
                    {component.validate?.required ? <span style={{ color: 'red' }}> *</span> : null}
                  </FormLabel>
                  <FormControl>
                    <Popover open={open} onOpenChange={setOpen}>
                      <PopoverTrigger asChild>
                        <Button variant="outline" className="w-full justify-between font-normal">
                          {field.value
                            ? new Date(field.value).toLocaleDateString()
                            : component.placeholder || 'Select date'}
                          <ChevronDownIcon className="ml-2 h-4 w-4" />
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent
                        className="w-auto rounded-lg border bg-white p-0 shadow-md"
                        align="start"
                      >
                        <Calendar
                          mode="single"
                          selected={field.value ? new Date(field.value) : undefined}
                          captionLayout="dropdown"
                          className={component.customClass}
                          fromYear={1900}
                          disabled={(date: Date) => {
                            const today = new Date();
                            const eighteenYearsAgo = new Date(
                              today.getFullYear() - 18,
                              today.getMonth(),
                              today.getDate(),
                            );
                            return date > eighteenYearsAgo || date < new Date('1900-01-01');
                          }}
                          toYear={new Date().getFullYear()}
                          onSelect={(date) => {
                            const iso = date ? date.toISOString() : '';
                            field.onChange(iso);
                            setOpen(false);
                          }}
                        />
                      </PopoverContent>
                    </Popover>
                  </FormControl>
                  {component.description && (
                    <FormDescription>{component.description}</FormDescription>
                  )}
                  <FormMessage />
                </FormItem>
              );
            }}
          />
        );

      case 'file':
        return <FileUpload component={component} form={form} />;

      case 'fieldset': {
        // Render a Card with optional label and recursively render child components
        return (
          <>
            {component.label && (
              <div className="border-b border-gray-200 pt-4 pb-2">
                <span className="text-base font-semibold">{component.label}</span>
              </div>
            )}
            <div className="space-y-4 px-2 py-6">
              {component.components?.map((child, idx) => (
                <FormioComponentRenderer
                  key={child.key || idx}
                  component={child}
                  form={form}
                  formSubmitSuccessCallback={formSubmitSuccessCallback}
                  formSubmitErrorCallback={formSubmitErrorCallback}
                />
              ))}
            </div>
          </>
        );
      }
      case 'htmlelement':
        return (
          <div
            className={`formio-html-element ${component.customClass}`}
            dangerouslySetInnerHTML={{ __html: component.content || '' }}
          />
        );

      case 'hidden':
        if (component?.properties?.customRender)
          return renderCustomModule(component.key, form, component.disabled);
        else
          return (
            <input
              type="hidden"
              name={component.key}
              className={component.customClass}
              value={form.getValues()[component.key!] ?? component.defaultValue ?? ''}
              readOnly
            />
          );
      case 'columns':
        return (
          <div
            className="grid gap-3"
            style={{ gridTemplateColumns: `repeat(${component?.columns?.length || 1}, 1fr)` }}
          >
            {component.columns?.map(
              (item, index) =>
                item.components?.[0] && (
                  <FormioComponentRenderer
                    key={index}
                    component={item.components[0]}
                    form={form}
                    formSubmitSuccessCallback={formSubmitSuccessCallback}
                    formSubmitErrorCallback={formSubmitErrorCallback}
                  />
                ),
            )}
          </div>
        );
      default:
        return (
          <div className="rounded border border-dashed border-gray-300 p-2">
            <span className="text-sm text-gray-500">
              Unsupported component type: {component.type}
            </span>
          </div>
        );
    }
  };

  return renderInput();
};
