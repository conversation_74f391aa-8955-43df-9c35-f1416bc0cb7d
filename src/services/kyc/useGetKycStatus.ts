import { apiUrls } from '@/constants/apiUrls';
import axiosInstance from '@/lib/api-client';
import { UserRole } from '@/types/user.types';
import { useQuery } from '@tanstack/react-query';

export interface KycResponseData {
  data: unknown;
  status: number;
}

export interface getKycPayload {
  keyword?: string;
  userType: UserRole;
}

const getKycStatus = async (payload: getKycPayload) => {
  const res = await axiosInstance.get<KycResponseData>(apiUrls.users.getKycStatusData(payload));
  return res.data;
};

export const useGetKycStatus = (payload: getKycPayload) => {
  return useQuery({
    queryFn: () => getKycStatus(payload),
    queryKey: ['kycStatus', payload.keyword, payload.userType],
    enabled: Bo<PERSON>an(payload.keyword),
  });
};
