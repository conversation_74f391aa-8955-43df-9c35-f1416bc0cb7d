import { Button, Input, Label } from '@kratex-tradetech/kratex-ui-library';
import React from 'react';
import useBasicInformationTab from './useBasicInformationTab';
import { Loader2, Loader2Icon, Save, User } from 'lucide-react';

const BasicInformationTab = () => {
  const { isPending, isUpdating, buyerInfo, saveUpdateBuyerInfo, updateBuyerInfoDetails } =
    useBasicInformationTab();

  if (!buyerInfo) {
    return (
      <div className="rounded-lg border bg-white p-6">
        <div className="py-12 text-center">
          <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-gray-100">
            <User className="h-8 w-8 text-gray-400" />
          </div>
          <h3 className="mb-1 text-lg font-medium text-gray-900">Basic Information</h3>
          <p className="text-gray-600">Information not available!</p>
        </div>
      </div>
    );
  }

  if (isPending) {
    return (
      <div className="flex h-[200px] items-center justify-center rounded-lg border bg-white p-6">
        <div className="flex items-center gap-2">
          <Loader2 className="h-4 w-4 animate-spin" />
          Loading basic information...
        </div>
      </div>
    );
  }

  return (
    <div className="rounded-lg border bg-white p-6">
      <div className="mb-6 flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium">Basic Information</h3>
          <p className="text-gray-600">
            {`Edit the buyer's primary details and contact information.`}
          </p>
        </div>
        <Button
          variant={'outline'}
          className="cursor-pointer"
          disabled={isUpdating}
          onClick={() => {
            saveUpdateBuyerInfo({ displayName: buyerInfo.displayName });
          }}
        >
          {isUpdating ? <Loader2Icon className="animate-spin" /> : <Save className="h-4 w-4" />}
          Save Changes
        </Button>
      </div>

      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
        <div className="space-y-2">
          <Label className="text-gray-600" htmlFor="displayName">
            Name <span className="text-red-500">*</span>
          </Label>
          <Input
            id="displayName"
            value={buyerInfo.displayName}
            onChange={(e) => updateBuyerInfoDetails('displayName', e.target.value)}
          />
        </div>

        <div className="space-y-2">
          <Label className="text-gray-600" htmlFor="email">
            Email
          </Label>
          <Input id="email" value={buyerInfo.email} disabled className="bg-gray-50" />
        </div>

        <div className="space-y-2">
          <Label className="text-gray-600" htmlFor="phone">
            Phone
          </Label>
          <Input id="phone" value={buyerInfo.phone} disabled className="bg-gray-50" />
        </div>

        <div className="space-y-2">
          <Label className="text-gray-600" htmlFor="orgType">
            Organisation Type
          </Label>
          <Input id="orgType" value={buyerInfo.organisationType} disabled className="bg-gray-50" />
        </div>

        <div className="space-y-2">
          <Label className="text-gray-600" htmlFor="state">
            State
          </Label>
          <Input id="state" value={buyerInfo.state} disabled className="bg-gray-50" />
        </div>

        <div className="space-y-2">
          <Label className="text-gray-600" htmlFor="country">
            Country
          </Label>
          <Input id="country" value={buyerInfo.country} disabled className="bg-gray-50" />
        </div>
      </div>
    </div>
  );
};

export default BasicInformationTab;
