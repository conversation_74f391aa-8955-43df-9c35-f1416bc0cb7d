import { toast } from 'sonner';
import { useSubmitKYCForm } from '@/services/kyc/useSubmitKYCForm';
import { useEffect } from 'react';
import { useFileIdsStore } from '@/store/fileIds.store';
import { useCustomDataStore } from '@/store/customData.store';
import { kycPayloadGenerator } from '@/utils/formio/kycPayloadGenerator';
import { PanVerifyResponse, VerifyAadhaarOtpResponse } from '@/types/sandboxVerification.types';
import { useGetFormDefinition } from '@/services/forms/useGetFormDefinition';
import { useRouter } from 'next/navigation';
import { KYCStatusData } from '@/constants/kyc-status';
import { staticZodSchemaGenerator } from '@/utils/formio/schema-generator';

const useSolePropriterIndia = () => {
  const router = useRouter();
  const {
    data: formSchema,
    isSuccess,
    isLoading,
    isError,
  } = useGetFormDefinition('kycBuyerSoleProprietorIndia');

  const { fileIds, clearFileIds } = useFileIdsStore();
  const { sandboxResponse, moduleData } = useCustomDataStore();

  const aadhaarResponse = sandboxResponse?.aadhaarResponse as VerifyAadhaarOtpResponse | undefined;
  const panVerification = sandboxResponse?.panVerification as PanVerifyResponse | undefined;

  const status = aadhaarResponse?.status || panVerification?.status;

  const { mutate: postKYCSolePropriterIndia, isPending: isPostKycPending } = useSubmitKYCForm({
    onSuccess: handleSubmitSuccess,
    onError: handleSubmitError,
  });

  useEffect(() => {
    if (isError) {
      toast.error('Error loading the KYC form, please try again.');
    }
  }, [isError]);

  const handleSubmit = (data: Record<string, string>) => {
    console.log(moduleData, 'moduleData');
    const result = staticZodSchemaGenerator(moduleData?.dropDownValue as string)?.safeParse(
      moduleData?.[moduleData?.dropDownValue as string],
    );

    console.log(result, 'result');

    if (result?.error) {
      toast.error(`Invalid ${moduleData?.dropDownValue}. Please try again.`);
      return;
    }

    // sandboxResponse will be null(in CustomVerificationDropdown) if other
    // vertification methods are chosen after verifying once or on
    // failed verification of a previous option(aadhar/pan etc)

    // if aadhaar/pan verified success only then send payload else user has to skip it
    // else if driving liscence/passport case, send payload with staus as 2 (under_review)
    if (sandboxResponse && status === 0) {
      if (formSchema) {
        const payload = kycPayloadGenerator(fileIds, formSchema.keyword, data, status);
        const { submission_data, ...rest } = payload;

        postKYCSolePropriterIndia({
          ...rest,
          submission_data: {
            ...(submission_data as Record<string, unknown>),
            ...(moduleData as Record<string, unknown>), // Spread moduleData into submission_data
          },
        });
      }
    } else if (!sandboxResponse) {
      if (formSchema) {
        const payload = kycPayloadGenerator(
          fileIds,
          formSchema.keyword,
          data,
          KYCStatusData.UNDER_REVIEW.value,
        );

        const { submission_data, ...rest } = payload;

        postKYCSolePropriterIndia({
          ...rest,
          submission_data: {
            ...(submission_data as Record<string, unknown>),
            ...(moduleData as Record<string, unknown>), // Spread moduleData into submission_data
          },
        });
      }
    } else {
      toast.error(
        data.message ||
          'KYC cannot be submitted as Aadhaar or PAN verification is not successful. Please try again later.',
      );
    }
  };

  const skipSubmit = () => router.push('/buyer/sourcing-intent');

  function handleSubmitSuccess() {
    toast.success('Kyc completed successfully!');
    clearFileIds();
    router.push('/buyer/sourcing-intent');
  }
  function handleSubmitError() {
    toast.error('Kyc failed!');
  }

  return {
    isError,
    formSchema: formSchema,
    isLoading,
    isSuccess,
    handleSubmit,
    skipSubmit,
    isPostKycPending,
  };
};

export default useSolePropriterIndia;
