'use client';

import { FormioForm<PERSON>enderer, FormioSchema } from '@/utils/formio';
import { Button } from '@kratex-tradetech/kratex-ui-library';
import { useDynamicZodForm } from '@/hooks/useDynamicZodForm';
import useBusinessDetails from './useBusinessDetails';
import { Loader2 } from 'lucide-react';

const SupplierBusinessDetailsForm = () => {
  const { handleSubmit, transformedSchema, isButtonDisabled, isFormLoading, isFormLoadError } =
    useBusinessDetails();

  const formioJson = transformedSchema as FormioSchema;

  const form = useDynamicZodForm({
    formioJson,
  });

  if (isFormLoading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <Loader2 className="animate-spin" />
      </div>
    );
  }
  if (isFormLoadError) {
    return (
      <div className="flex h-screen items-center justify-center">
        <p>Failed to load form, please try again later.</p>
      </div>
    );
  }

  return (
    <FormioFormRenderer form={form} formioJson={formioJson} onSubmit={handleSubmit}>
      <Button disabled={isButtonDisabled} className="ml-auto block" type="submit">
        Proceed
      </Button>
    </FormioFormRenderer>
  );
};

export default SupplierBusinessDetailsForm;
