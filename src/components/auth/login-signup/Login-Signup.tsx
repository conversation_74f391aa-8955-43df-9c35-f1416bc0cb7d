'use client';

import {
  AuthLayout,
  OtpVerificationForm,
  PhoneInputForm,
} from '@kratex-tradetech/kratex-ui-library';
import { useLoginSignup } from './useLoginSignup';

const LoginPage = () => {
  const {
    phoneNumber,
    setPhoneNumber,
    role,
    setRoles,
    loading,
    otpSent,
    otp,
    setOtp,
    // user,
    // Handlers
    handlePhoneSubmit,
    handleOtpSubmit,
    handleChangePhone,
    handleResendOTP,
    ROLES,
  } = useLoginSignup();

  // // Show dashboard if authenticated
  // if (user?.isRegistered) {
  //   return (
  //     <div className="flex min-h-screen items-center justify-center">
  //       <div className="text-center">
  //         <div className="mx-auto mb-2 h-8 w-8 animate-spin rounded-full border-b-2 border-blue-600"></div>
  //         <p>Redirecting to dashboard...</p>
  //       </div>
  //     </div>
  //   );
  // }

  return (
    <AuthLayout
      title={otpSent ? 'Verify OTP' : 'Login to the KrateX Marketplace'}
      subtitle={
        otpSent ? `Enter the OTP sent to ${phoneNumber}` : 'Enter your phone number to continue'
      }
      dashboardImageUrl="https://images.unsplash.com/photo-1551434678-e076c223a692?w=800&h=600&fit=crop&crop=center"
      logoImageUrl="https://www.thiings.co/_next/image?url=https%3A%2F%2Flftz25oez4aqbxpq.public.blob.vercel-storage.com%2Fimage-z0ZhVjqJ2YRczY2UMkZt14vdtiFTiZ.png&w=1920&q=75"
      footerLinks={{
        mainLink: { text: '© 2024 Cusana Inc. All rights reserved.', to: '/' },
        privacyLink: { text: 'Privacy Policy', to: '/privacy' },
        termsLink: { text: 'Terms & Conditions', to: '/terms' },
      }}
    >
      {!otpSent ? (
        <PhoneInputForm
          phoneNumber={phoneNumber}
          setPhoneNumber={(value) => setPhoneNumber(value ?? '')}
          roles={ROLES}
          role={{ id: role, name: ROLES.find((r) => r.id === role)?.name || '' }}
          setRole={(selectedRole: { id: number; name: string }) => setRoles(selectedRole.id)}
          onSubmit={handlePhoneSubmit}
          loading={loading}
          submitLabel="Continue"
        />
      ) : (
        <OtpVerificationForm
          otp={otp}
          setOtp={setOtp}
          onSubmit={handleOtpSubmit}
          onChangePhone={handleChangePhone}
          onResendOtp={handleResendOTP}
          loading={loading}
          submitLabel="Verify & Login"
          changePhoneLabel="Change Phone Number"
        />
      )}
    </AuthLayout>
  );
};

export default LoginPage;
