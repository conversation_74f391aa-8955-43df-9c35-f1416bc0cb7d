'use client';

import { useEffect, useState, useRef, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import { newSupplierStore } from '@/store/newSupplier.store';
import { Country } from '@/types/newSupplier.types';
import { useCountries } from '@/services/geolocation/useGetJurisdiction';
import { useGeoLookup } from '@/services/geolocation/useGeoLookup';
import { DropdownOption } from '@/components/common/MultiSelectDropdown/types';
import { transformUICountryToInternal } from '@/utils/countryTransform';

interface GeoLookupData {
  country_code_iso3: string;
}

function isCountry(country: any): country is Country {
  return (
    typeof country?.iso2 === 'string' &&
    typeof country?.iso3 === 'string' &&
    typeof country?.name === 'string' &&
    typeof country?.emoji === 'string' &&
    typeof country?.phone_code === 'string'
  );
}

export const useJurisdiction = () => {
  const router = useRouter();
  const { setJurisdiction, newSupplierData } = newSupplierStore();

  const [countryOfOrigin, setCountryOfOrigin] = useState<Country | null>(
    newSupplierData?.jurisdiction.countryOfOrigin || null,
  );

  const [countriesToServe, setCountriesToServe] = useState<Country[]>(
    newSupplierData?.jurisdiction.countriesToServe || [],
  );

  const [countrySearch, setCountrySearch] = useState('');
  const [countriesServeSearch, setCountriesServeSearch] = useState('');
  const [isCountrySelectOpen, setIsCountrySelectOpen] = useState(false);
  const [isCountriesServeSelectOpen, setIsCountriesServeSelectOpen] = useState(false);

  const geoLookupTriedRef = useRef(false);

  const {
    data: countries = [],
    isLoading: loadingCountries,
    error: countriesError,
  } = useCountries();

  const {
    data: geoData,
    refetch: geoRefetch,
    isSuccess: geoSuccess,
    isLoading: isGeoLookupLoading,
  } = useGeoLookup();

  const fallbackToIndia = (): Country | null => {
    const india = countries.find((c) => c.alpha2 === 'IN');
    return isCountry(india) ? india : null;
  };

  // Geo lookup trigger
  useEffect(() => {
    if (
      !geoLookupTriedRef.current &&
      !countryOfOrigin &&
      !loadingCountries &&
      countries.length > 0
    ) {
      geoLookupTriedRef.current = true;
      geoRefetch();
    }
  }, [loadingCountries, countries, countryOfOrigin, geoRefetch]);

  useEffect(() => {
    if (geoSuccess && geoData) {
      const geo = geoData as GeoLookupData;
      const match = countries.find((c) => c.iso3 === geo.country_code_iso3);
      setCountryOfOrigin(isCountry(match) ? match : fallbackToIndia());
    }
  }, [geoSuccess, geoData, countries]);

  const validateForm = () => {
    return !!(countryOfOrigin && countriesToServe.length > 0);
  };

  const onJurisdictionSubmit = () => {
    if (validateForm()) {
      setJurisdiction({
        countryOfOrigin: countryOfOrigin!,
        countriesToServe: countriesToServe,
      });
      console.log(countryOfOrigin, countriesToServe);
      router.push('/supplier/org-type');
    }
  };

  const handleCountryOfOriginSelect = (uiCountry: any) => {
    if (!uiCountry?.alpha2) return;
    const transformedCountry = transformUICountryToInternal(uiCountry);
    if (!transformedCountry) return;
    setCountryOfOrigin(transformedCountry);
  };

  const handleCountryToServeToggle = (country: Country) => {
    setCountriesToServe((prevSelected) => {
      const exists = prevSelected.some((c) => c.id === country.id);
      return exists ? prevSelected.filter((c) => c.id !== country.id) : [...prevSelected, country];
    });
  };

  const handleMultiSelectCountryToServe = (option: DropdownOption) => {
    const countryObj = countries.find((c) => c.id === option.id);
    if (countryObj) {
      handleCountryToServeToggle(countryObj);
    }
  };

  const clearCountrySearch = () => setCountrySearch('');
  const clearCountriesServeSearch = () => setCountriesServeSearch('');

  const progress = useMemo(() => {
    return countryOfOrigin && countriesToServe.length > 0
      ? 20
      : countryOfOrigin || countriesToServe.length > 0
        ? 10
        : 0;
  }, [countryOfOrigin, countriesToServe]);

  const filteredCountriesForOrigin = useMemo(() => {
    if (!countrySearch.trim()) return countries;
    const term = countrySearch.toLowerCase().trim();
    return countries.filter(
      (country) => country.name.toLowerCase().includes(term) || country.emoji?.includes(term),
    );
  }, [countries, countrySearch]);

  const filteredCountriesForServe = useMemo(() => {
    if (!countriesServeSearch.trim()) return countries;
    const term = countriesServeSearch.toLowerCase().trim();
    return countries.filter(
      (country) => country.name.toLowerCase().includes(term) || country.emoji?.includes(term),
    );
  }, [countries, countriesServeSearch]);

  const countryServeOptions: DropdownOption[] = filteredCountriesForServe.map((country) => ({
    id: country.id,
    name: country.name,
    emoji: country.emoji,
  }));

  const selectedCountryServeOptions: DropdownOption[] = countriesToServe.map((country) => ({
    id: country.id,
    name: country.name,
    emoji: country.emoji,
  }));

  const resetJurisdiction = () => {
    setCountryOfOrigin(null);
    setCountriesToServe([]);
  };

  return {
    countries,
    countryOfOrigin,
    countriesToServe,
    countrySearch,
    countriesServeSearch,
    setCountrySearch,
    setCountriesServeSearch,
    clearCountrySearch,
    clearCountriesServeSearch,
    filteredCountriesForOrigin,
    filteredCountriesForServe,
    countryServeOptions,
    selectedCountryServeOptions,
    loadingCountries,
    isGeoLookupLoading,
    countriesError,
    isCountrySelectOpen,
    setIsCountrySelectOpen,
    isCountriesServeSelectOpen,
    setIsCountriesServeSelectOpen,
    setCountryOfOrigin,
    setCountriesToServe,
    handleCountryOfOriginSelect,
    handleCountryToServeToggle,
    handleMultiSelectCountryToServe,
    validateForm,
    onJurisdictionSubmit,
    resetJurisdiction,
    progress,
  };
};
