'use client';

import { <PERSON>, <PERSON>Header, Card<PERSON><PERSON>le, CardContent } from '@kratex-tradetech/kratex-ui-library';
import { Globe } from 'lucide-react';

interface jurisdictionData {
  state: string;
  country: string;
}

interface JurisdictionCardProps {
  data: jurisdictionData;
}

const JurisdictionCard: React.FC<JurisdictionCardProps> = ({ data }) => {
  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Globe className="h-4 w-4" />
            Jurisdiction Information
          </CardTitle>
        </div>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <div>
            <p className="text-sm font-medium text-gray-600">Country</p>
            <p className="text-sm">{data.country}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-600">State</p>
            <p className="text-sm">{data.state}</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default JurisdictionCard;
