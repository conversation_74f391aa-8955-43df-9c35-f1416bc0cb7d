import { OnboardingState } from '@/constants/onboarding-status';

export type UserRole = 'buyer' | 'supplier';

export enum RoleEnum {
  ADMIN = 1,
  BUYER = 2,
  SUPPLIER = 3,
}

export interface User {
  role: RoleEnum | 0;
  GivenName: string;
  Surname: string;
  Email: string;
  iss?: string;
  iat?: number;
  exp?: number;
  aud?: string;
  sub?: string;
  isRegistered?: boolean;
}

export interface UserMeResponse {
  id: number;
  name: string;
  email: string;
  phone: string;
  role: number;
  onboarding_state: OnboardingState;
  user_type: number;
  tenant: Tenant;
}

export interface Tenant {
  id: number;
  name: string;
  kyc_status: number;
}
