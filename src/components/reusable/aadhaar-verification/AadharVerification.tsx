'use client';
import {
  Button,
  Input,
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
  Label,
} from '@kratex-tradetech/kratex-ui-library';
import { useAadhaar } from './useAadhaar';
import { CheckCircle, RefreshCw, Send } from 'lucide-react';

interface AadharVerificationProps {
  required?: boolean;
}

const AadharVerification = ({ required = true }: AadharVerificationProps) => {
  const {
    otp,
    setOtp,
    aadhaarNumber,
    setAadhaarNumber,
    handleOtpSend,
    isVerifyError,
    handleVerifyOtp,
    isOtpSentSuccess,
    isPending,
    isOtpVerified,
    otpSent,
  } = useAadhaar();

  return (
    <div className="flex w-full flex-col items-start justify-center">
      <Label className="mb-2">
        Aadhaar number {required && <span className="text-red-500">*</span>}
      </Label>
      {isOtpVerified ? (
        <div className="relative">
          <Input type="number" value={aadhaarNumber} disabled />
          <CheckCircle className="absolute top-1/2 right-1 h-4 w-4 -translate-y-1/2 transform text-green-500" />
        </div>
      ) : (
        <>
          <div className="mb-4 flex flex-row items-center gap-4">
            <Input
              placeholder="Enter Aadhaar number"
              maxLength={12}
              className="w-fit"
              minLength={12}
              type="text"
              pattern="^\d{12}$"
              value={aadhaarNumber}
              onChange={(e) => setAadhaarNumber(e.target.value)}
            />
            <Button
              disabled={isOtpSentSuccess || !/^\d{12}$/.test(aadhaarNumber)}
              onClick={handleOtpSend}
            >
              <Send className="mr-2 h-3 w-3" />
              Send OTP
            </Button>
          </div>
          {!/^\d{12}$/.test(aadhaarNumber) && aadhaarNumber.length > 1 && (
            <p className="mt-2 text-sm text-red-500">Please enter a correct Aadhar number.</p>
          )}

          {otpSent && (
            <>
              <InputOTP maxLength={6} inputMode="numeric" onChange={(e) => setOtp(e)} value={otp}>
                <InputOTPGroup>
                  <InputOTPSlot index={0} />
                  <InputOTPSlot index={1} />
                  <InputOTPSlot index={2} />
                  <InputOTPSlot index={3} />
                  <InputOTPSlot index={4} />
                  <InputOTPSlot index={5} />
                </InputOTPGroup>
              </InputOTP>
              <div className="my-4 flex flex-row items-start justify-start gap-4">
                <Button disabled={isPending} onClick={handleVerifyOtp}>
                  Verify OTP
                </Button>
                <Button onClick={handleOtpSend}>
                  <RefreshCw className="mr-2 h-3 w-3" />
                  Resend OTP
                </Button>
              </div>
            </>
          )}
          {isVerifyError && (
            <p className="mt-2 text-sm text-red-500">Invalid OTP. Please try again.</p>
          )}
        </>
      )}
    </div>
  );
};

export default AadharVerification;
