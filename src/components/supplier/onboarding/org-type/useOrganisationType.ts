'use client';

import { useMemo, useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  useNewSupplierJurisdiction,
  newSupplierStore,
  useNewSupplierRole,
} from '@/store/newSupplier.store';
import { useGetOrganisationTypes } from '@/services/auth/useGetOrganisationType';
import { OrganisationTypeOption } from './types';

export const useOrganisationType = () => {
  const router = useRouter();
  const supplierJurisdiction = useNewSupplierJurisdiction();
  const role = useNewSupplierRole();
  const [selectedTypes, setSelectedTypes] = useState<string[]>([]);
  const { setOrgType } = newSupplierStore();

  const {
    data: apiOrganisationTypes = [],
    isLoading,
    error,
  } = useGetOrganisationTypes(supplierJurisdiction?.countryOfOrigin?.iso2, String(role));

  const staticOrganisationTypes: OrganisationTypeOption[] = [
    { id: 1, name: 'Manufacturer' },
    { id: 2, name: 'Brand owner' },
    { id: 3, name: 'Authorised Distributor/Dealer' },
    { id: 4, name: 'Trader/Wholesaler' },
    { id: 5, name: 'Service provider' },
  ];

  const organisationTypes: OrganisationTypeOption[] = useMemo(() => {
    return apiOrganisationTypes.length > 0 ? apiOrganisationTypes : staticOrganisationTypes;
  }, [apiOrganisationTypes]);

  const progress = useMemo(() => (selectedTypes.length > 0 ? 20 : 10), [selectedTypes]);

  const proceed = () => {
    if (selectedTypes.length === 0 || isLoading || error) return;

    if (selectedTypes.length > 0) {
      const selectedOrgs = organisationTypes.filter((type: OrganisationTypeOption) =>
        selectedTypes.includes(type.name),
      );
      if (selectedOrgs.length > 0) {
        setOrgType(selectedOrgs.map((org) => ({ id: org.id, name: org.name })));
      }
      router.push('/supplier/account-basics');
    }
  };

  // Multi-select handler
  const handleTypeToggle = (typeName: string) => {
    setSelectedTypes((prev) =>
      prev.includes(typeName) ? prev.filter((name) => name !== typeName) : [...prev, typeName],
    );
  };

  return {
    isLoading,
    selectedTypes,
    setSelectedTypes,
    organisationTypes,
    proceed,
    error,
    progress,
    handleTypeToggle,
  };
};
