import apiClient from '@/lib/api-client';
import { UseMutationHookProps } from '@/types/api';
import { BuyerInfoResponse, UpdateBuyerInfoPayload } from '@/types/newBuyer.types';
import { useMutation } from '@tanstack/react-query';
import { AxiosError } from 'axios';

async function updateBuyerInfo(payload: UpdateBuyerInfoPayload) {
  const response = await apiClient.post<BuyerInfoResponse>('/api/v1/users/buyer/info/', payload);
  return response.data;
}

export const useUpdateBuyerInfo = ({
  onSuccess,
  onError,
}: UseMutationHookProps<unknown, { message: string }>) => {
  return useMutation<unknown, AxiosError<{ message: string }>, UpdateBuyerInfoPayload>({
    mutationFn: (payload) => updateBuyerInfo(payload),
    onError,
    onSuccess,
  });
};
