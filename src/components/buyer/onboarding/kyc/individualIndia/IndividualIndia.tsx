'use client';

import { Formio<PERSON>orm<PERSON><PERSON>er, FormioSchema } from '@/utils/formio';
import useIndividualIndia from './useIndividualIndia';
import { Loader } from 'lucide-react';
import { Button } from '@kratex-tradetech/kratex-ui-library';
import { useDynamicZodForm } from '@/hooks/useDynamicZodForm';
import { FormioFormResponse } from '@/utils/formio/types';
import useKycSkip from '../useKycSkip';

const IndividualIndiaComponent = ({ formSchema }: { formSchema: FormioFormResponse }) => {
  const { isLoading, isSuccess, handleSubmit, isPostKycPending } = useIndividualIndia();
  const { skipKyc, isSkipPending } = useKycSkip();
  const form = useDynamicZodForm({
    formioJson: formSchema?.schema as FormioSchema,
  });
  if (isLoading) return <Loader className="animate-spin" />;

  return (
    isSuccess &&
    formSchema && (
      <div className="flex min-h-screen flex-col items-center justify-center p-8">
        <div className="mb-6 text-center">
          <h2 className="mb-2 text-2xl font-semibold">
            KYC Verification - Individual Buyer (India)
          </h2>
          <p className="text-gray-600">
            Kindly provide your details below to complete the KYC verification process as an
            individual buyer in India.
          </p>
        </div>
        <FormioFormRenderer
          form={form}
          onSubmit={handleSubmit}
          formioJson={formSchema.schema as FormioSchema}
        >
          <div className="flex w-full flex-row items-center justify-between gap-x-4">
            <Button
              className="flex w-[calc(50%-8px)]"
              type="button"
              variant="outline"
              onClick={skipKyc}
              disabled={isPostKycPending || isSkipPending}
            >
              Skip
            </Button>
            <Button
              className="flex w-[calc(50%-8px)]"
              type="submit"
              disabled={isPostKycPending || isSkipPending}
            >
              Subimit
            </Button>
          </div>
        </FormioFormRenderer>
      </div>
    )
  );
};

const IndividualIndia = () => {
  const { formSchema } = useIndividualIndia();

  return formSchema ? (
    <IndividualIndiaComponent formSchema={formSchema as FormioFormResponse} />
  ) : (
    <div className="flex h-screen items-center justify-center">
      <Loader className="animate-spin" />
    </div>
  );
};
export default IndividualIndia;
