import { clearTokenFromCookie } from '@/lib/cookies';
import { newBuyerStore } from '@/store/newBuyer.store';
import { userStore } from '@/store/user.store';

const useLogout = () => {
  const handleLogout = () => {
    userStore.getState().clearUser();
    newBuyerStore.getState().clearNewBuyerData();
    clearTokenFromCookie();
    // Redirect to login
    window.location.href = '/login-signup';
  };
  return { handleLogout };
};

export default useLogout;
