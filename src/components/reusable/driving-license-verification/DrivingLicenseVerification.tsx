'use client';

import { useCustomDataStore } from '@/store/customData.store';
import {
  FormControl,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
} from '@kratex-tradetech/kratex-ui-library';
import { useState } from 'react';

const DrivingLicenseVerification = () => {
  const { clearAllSandboxResponse, setModuleData } = useCustomDataStore();
  const [drivingLicense, setDrivingLicense] = useState('');

  const handleBlur = () => {
    clearAllSandboxResponse();
    setModuleData({ drivingLicense });
  };

  return (
    <>
      <FormItem className="space-x-2">
        <FormLabel>
          Driving License Number <span className="text-red-500">*</span>
        </FormLabel>
        <FormControl>
          <div className="relative">
            <Input
              value={drivingLicense}
              onChange={(e) => setDrivingLicense(e.target.value)}
              onBlur={handleBlur}
              placeholder="Enter your driving license number"
            />
          </div>
        </FormControl>
        <FormMessage />
      </FormItem>
    </>
  );
};

export default DrivingLicenseVerification;
