'use client';

import React from 'react';
import SourcingIntentDisplay from './SourcingIntentDisplay';
import EditSourcingIntentModal from './EditSourcingIntentModal';
import { useSourcingIntentManagement } from './useSourcingIntentManagement';

interface SourcingIntentDisplayContainerProps {
  userType?: 'buyer' | 'seller';
  showEditButton?: boolean;
  title?: string;
  icon?: React.ReactNode;
  className?: string;
  variant?: 'card' | 'inline';
  onUpdateSuccess?: () => void;
  onUpdateError?: (error: any) => void;
}

const SourcingIntentDisplayContainer: React.FC<SourcingIntentDisplayContainerProps> = ({
  userType = 'buyer',
  showEditButton = true,
  title,
  icon,
  className,
  variant = 'card',
  onUpdateSuccess,
  onUpdateError,
}) => {
  const {
    displayData,
    formData,
    setFormData,
    isLoading,
    isUpdating,
    isModalOpen,
    openModal,
    closeModal,
    handleUpdate,
    NO_PREFERENCE_OPTION,
  } = useSourcingIntentManagement({
    userType,
    onUpdateSuccess,
    onUpdateError,
  });

  if (isLoading) {
    return (
      <div className="animate-pulse">
        <div className="h-48 rounded-lg bg-gray-200"></div>
      </div>
    );
  }

  if (!displayData) {
    return (
      <div className="py-8 text-center">
        <p className="text-gray-500">No sourcing intent data available</p>
      </div>
    );
  }

  return (
    <>
      <SourcingIntentDisplay
        data={displayData}
        onEdit={showEditButton ? openModal : undefined}
        showEditButton={showEditButton}
        title={title}
        icon={icon}
        className={className}
        variant={variant}
      />

      {showEditButton && (
        <EditSourcingIntentModal
          isOpen={isModalOpen}
          onClose={closeModal}
          formData={formData}
          setFormData={setFormData}
          onSave={handleUpdate}
          NO_PREFERENCE_OPTION={NO_PREFERENCE_OPTION}
          isLoading={isUpdating}
        />
      )}
    </>
  );
};

export default SourcingIntentDisplayContainer;
