export type OrgType =
  | 'INDIVIDUAL'
  | 'SOLE_PROPRIETOR'
  | 'REGISTERED_COMPANY'
  | 'GOVERNMENT_BODY'
  | 'NONPROFIT'
  | 'BUYING_AGENT';
export type SeatRole = 'PROCUREMENT' | 'FINANCE' | 'SUPPORT' | 'VIEW_ONLY';

export interface PersonalInfo {
  full_name: string;
  email: string;
  phone_number: string;
  country: string;
  state: string;
}

export interface OrganizationInfo {
  org_type: OrgType;
  org_name: string;
  registration_number: string;
  tax_id: string;
  business_address: string;
}

export enum UseCaseEnum {
  Resale = 1,
  OwnManufacture = 2,
  RnDSample = 3,
  Dropship = 4,
  GovtProject = 5,
}

export enum ShippingTermEnum {
  EXW = 1,
  FOB = 2,
  CIF = 3,
  DDP = 4,
  Open = 5,
}

export enum LeadTimeEnum {
  LessThan7Days = 1,
  OneToFourWeeks = 2,
  MoreThan4Weeks = 3,
  Prototype = 4,
}

export interface SourcingIntent {
  sourcing_for: string[];
  sourcing_from: string[];
  use_case: string;
  use_case_enum?: UseCaseEnum;
  product_categories: string[];
  compliance_reqs: string[];
  compliance_reqs_enum?: number[];
  pref_shipping_term: string;
  pref_shipping_term_enum?: ShippingTermEnum;
  target_lead_time: string;
  target_lead_time_enum?: LeadTimeEnum;
}

export interface TeamMember {
  id: string;
  full_name: string;
  phone_number: string;
  invite_email: string;
  seat_role: SeatRole;
}

export interface OnboardingData {
  personal_info: PersonalInfo;
  organization_info: OrganizationInfo;
  sourcing_intent: SourcingIntent;
  team_members: TeamMember[];
  kyc_status: 'PASSED' | 'PENDING' | 'FAILED';
}
