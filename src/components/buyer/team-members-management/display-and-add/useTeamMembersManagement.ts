import { useState } from 'react';
import { useGetInvitedTeamMembers } from '@/services/useGetInvitedTeam';

const useTeamMembersManagement = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const {
    data: teamMembersData,
    isError,
    isLoading,
    isSuccess,
  } = useGetInvitedTeamMembers('buyer');

  // Modal handlers
  const openModal = () => setIsModalOpen(true);
  const closeModal = () => setIsModalOpen(false);

  return {
    displayData: teamMembersData,

    isLoading,
    isError,
    isSuccess,

    // Modal states
    isModalOpen,
    openModal,
    closeModal,
  };
};

export default useTeamMembersManagement;
