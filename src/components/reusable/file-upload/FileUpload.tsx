import { FormioComponent } from '@/utils/formio';
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormDescription,
  FormMessage,
  Button,
  Input,
} from '@kratex-tradetech/kratex-ui-library';
import { CloudUpload, FileCheck, Info, Upload, X } from 'lucide-react';
import React from 'react';
import { UseFormReturn } from 'react-hook-form';
import useFileUpload from './useFileUpload';

const FileUpload = ({ form, component }: { form: UseFormReturn; component: FormioComponent }) => {
  const { onSubmit, isPending } = useFileUpload(component.key!);

  const fileData = form.watch(component.key!);

  return (
    <>
      <FormField
        control={form.control}
        name={component.key!}
        render={({ field }) => (
          <FormItem>
            <FormLabel>
              {component.label}
              {component.validate?.required ? <span style={{ color: 'red' }}> *</span> : null}
            </FormLabel>
            <FormControl>
              <div className="relative">
                <label
                  htmlFor={`file-upload-${component.key}`}
                  className="group flex h-32 w-full cursor-pointer items-center justify-center rounded-lg border-2 border-dashed border-gray-300 transition-colors hover:border-gray-400"
                >
                  <Input
                    name={component.key}
                    id={`file-upload-${component.key}`}
                    type="file"
                    className="absolute inset-0 h-full w-full cursor-pointer opacity-0"
                    onChange={(e) => field.onChange(e.target.files?.[0] ?? null)}
                  />
                  <div className="flex flex-col items-center space-y-2 text-gray-500 group-hover:text-gray-600">
                    {field.value ? (
                      <>
                        <FileCheck className="h-8 w-8" />
                        <div className="text-center">
                          <p className="text-sm font-medium">{(field.value as File).name}</p>
                          <p className="text-xs">
                            {((field.value as File).size / 1024 / 1024).toFixed(2)} MB
                          </p>
                        </div>
                      </>
                    ) : (
                      <>
                        <Upload className={`size-8 ${component.customClass}`} />
                        <div className="text-center">
                          <p className="text-sm font-medium">Click to upload file</p>
                          <p className="text-xs">or drag and drop</p>
                        </div>
                      </>
                    )}
                  </div>
                </label>

                {field.value && (
                  <button
                    type="button"
                    onClick={() => form.setValue(component.key!, null)}
                    className="absolute -top-2 -right-2 flex h-6 w-6 items-center justify-center rounded-full border border-gray-300 bg-white transition-colors hover:bg-gray-50"
                  >
                    <X className="h-3 w-3" />
                  </button>
                )}
              </div>
            </FormControl>
            {component.description && (
              <FormDescription className="flex items-center space-x-1">
                <Info className="h-3 w-3" />
                <span>{component.description}</span>
              </FormDescription>
            )}
            <FormMessage />
          </FormItem>
        )}
      />
      <div className="ml-auto block h-fit w-fit">
        <Button disabled={isPending || !fileData} type="button" onClick={() => onSubmit(fileData)}>
          Submit <CloudUpload />
        </Button>
      </div>
    </>
  );
};

export default FileUpload;
