/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { z } from 'zod';
import type { FormioComponent, FormioColumnsComponent } from './types';

/**
 * Generates a Zod validation schema based on Form.io components.
 */
export const createZodSchema = (components: Array<FormioComponent | FormioColumnsComponent>) => {
  const fields: Record<string, z.ZodTypeAny> = {};

  const handleField = (comp: FormioComponent) => {
    if (!comp.key || !comp.input) return;
    const v = comp.validate || {};
    const opts = comp.data?.values ?? comp.values;

    let schema: z.ZodTypeAny;

    switch (comp.type) {
      case 'textfield':
      case 'textarea':
        schema = z.string();
        break;
      case 'email':
        schema = z.string().email('Invalid email address');
        break;
      case 'number':
        schema = z.number();
        break;
      case 'datetime':
        schema = z.string().refine((val) => !isNaN(Date.parse(val)), {
          message: `${comp.label || comp.key} must be a valid date`,
        });
        break;
      case 'select':
      case 'radio':
        schema = z.string();
        break;
      case 'checkbox':
        if (opts && opts.length > 0) {
          // checkbox group
          schema = z.array(z.string());
        } else {
          // single checkbox
          schema = z.boolean();
        }
        break;
      case 'file':
        // File validation logic based on required status
        if (v.required) {
          // Required file: must be File instance with all validations
          schema = z
            .instanceof(File, { message: 'This is a required field.' })
            .refine((val) => val instanceof File, {
              message: `${comp.label || comp.key} must be a file`,
            })
            .refine((file) => (file as File).size <= 5 * 1024 * 1024, {
              message: `${comp.label || comp.key} must be ≤ 5 MB`,
            })
            .refine(
              (file) =>
                ['image/png', 'image/jpeg', 'image/jpg', 'application/pdf'].includes(
                  (file as File).type,
                ),
              {
                message: `${comp.label || comp.key} must be JPG, PNG or PDF`,
              },
            );
        } else {
          // Optional file: allow undefined/null OR valid file
          schema = z
            .any()
            .optional()
            .refine(
              (val) => {
                // If no file uploaded, it's valid
                if (!val || val === null || val === undefined) return true;
                // If file uploaded, it must be a File instance
                return val instanceof File;
              },
              {
                message: `${comp.label || comp.key} must be a file`,
              },
            )
            .refine(
              (val) => {
                // If no file uploaded, it's valid
                if (!val || val === null || val === undefined) return true;
                // If file uploaded, check size
                return (val as File).size <= 5 * 1024 * 1024;
              },
              {
                message: `${comp.label || comp.key} must be ≤ 5 MB`,
              },
            )
            .refine(
              (val) => {
                // If no file uploaded, it's valid
                if (!val || val === null || val === undefined) return true;
                // If file uploaded, check type
                return ['image/png', 'image/jpeg', 'image/jpg', 'application/pdf'].includes(
                  (val as File).type,
                );
              },
              {
                message: `${comp.label || comp.key} must be JPG, PNG or PDF`,
              },
            );
        }
        break;

      default:
        schema = z.any();
    }

    // Handle required validation for non-file fields
    if (v.required && comp.type !== 'file') {
      if (comp.type === 'checkbox' && opts && opts.length > 0) {
        schema = (schema as z.ZodArray<z.ZodString>).min(1, `This is a required field.`);
      } else if (comp.type === 'checkbox') {
        schema = (schema as z.ZodBoolean).refine((val) => val === true, {
          message: `Required`,
        });
      } else if (schema instanceof z.ZodString) {
        schema = schema.min(1, `This is a required field.`);
      }
    } else if (!v.required && comp.type !== 'file') {
      // Only make non-file fields optional here
      schema = schema.optional();
    }

    // string patterns
    if (v.pattern && schema instanceof z.ZodString) {
      const rx = new RegExp(v.pattern);
      schema = schema.refine((str) => !str || rx.test(str), {
        message: v.patternMessage || 'Invalid format',
      });
    }

    // lengths
    if (v.minLength && schema instanceof z.ZodString) {
      schema = schema.min(Number(v.minLength), `Minimum ${v.minLength} characters`);
    }
    if (v.maxLength && schema instanceof z.ZodString) {
      schema = schema.max(Number(v.maxLength), `Maximum ${v.maxLength} characters`);
    }

    // number range
    if (comp.type === 'number' && schema instanceof z.ZodNumber) {
      if (v.min !== undefined)
        schema = (schema as z.ZodNumber).min(v.min, `Minimum value is ${v.min}`);
      if (v.max !== undefined)
        schema = (schema as z.ZodNumber).max(v.max, `Maximum value is ${v.max}`);
    }

    if (comp.key === 'gstnVerifcationModule') {
      fields['gstin'] = z.string().min(15, 'GST Number must be 15 characters');
    }

    fields[comp.key] = schema;
  };

  const recurse = (comps: Array<FormioComponent | FormioColumnsComponent>) => {
    comps.forEach((c) => {
      if (c.type === 'columns') {
        (c as FormioColumnsComponent).columns.forEach((col) => recurse(col.components));
      } else if ((c as FormioComponent).components) {
        recurse((c as FormioComponent).components!);
      } else {
        handleField(c as FormioComponent);
      }
    });
  };

  recurse(components);
  return z.object(fields);
};

/**
 * Builds default values for React Hook Form based on Form.io components.
 */
export const getDefaultValues = (components: Array<FormioComponent | FormioColumnsComponent>) => {
  const defaults: Record<string, any> = {};

  const handleDefault = (comp: FormioComponent) => {
    if (!comp.key || !comp.input) return;

    // adding the custom modules
    // TODO: refactor to a seperate function in future
    if (comp.key === 'gstnVerifcationModule') {
      defaults['gstin'] = comp.defaultValue;
    }

    if (comp.defaultValue !== undefined) {
      defaults[comp.key] = comp.defaultValue;
      return;
    }
    // set sensible defaults for checkbox groups
    if (comp.type === 'checkbox' && (comp.data?.values ?? comp.values)?.length) {
      defaults[comp.key] = [];
    }
  };

  const recurse = (comps: Array<FormioComponent | FormioColumnsComponent>) => {
    comps.forEach((c) => {
      if (c.type === 'columns') {
        (c as FormioColumnsComponent).columns.forEach((col) => recurse(col.components));
      } else if ((c as FormioComponent).components) {
        recurse((c as FormioComponent).components!);
      } else {
        handleDefault(c as FormioComponent);
      }
    });
  };

  recurse(components);
  return defaults;
};

const drivingLicenceSchema = z.string();

const panNumberSchema = z
  .string()
  .regex(/^[A-Z]{5}[0-9]{4}[A-Z]$/, 'Invalid PAN number format')
  .min(10, 'PAN number must be 10 characters')
  .max(10, 'PAN number must be 10 characters');

const passportNumberSchema = z
  .string()
  .regex(
    /^[A-Z0-9]{6,9}$/,
    'Invalid passport number format: must be 6-9 uppercase letters or digits, no spaces or special characters',
  );

const aadhaarNumberSchema = z
  .string()
  .regex(/^[2-9]{1}[0-9]{11}$/, 'Invalid Aadhaar number format')
  .min(12, 'Aadhaar number must be 12 digits')
  .max(12, 'Aadhaar number must be 12 digits');

export const staticZodSchemaGenerator = (schemaType: string) => {
  switch (schemaType) {
    case 'drivingLicence':
      return drivingLicenceSchema;
    case 'pan':
      return panNumberSchema;
    case 'passport':
      return passportNumberSchema;
    case 'aadhaar':
      return aadhaarNumberSchema;
    default:
      console.error('returning null schema');
      return null;
  }
};
