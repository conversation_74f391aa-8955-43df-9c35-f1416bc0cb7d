'use client';

import {
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  Progress,
} from '@kratex-tradetech/kratex-ui-library';

import { CheckCircle, Sparkles } from 'lucide-react';
import { useState } from 'react';

export default function OnboardingCompletion() {
  const [progress, setProgress] = useState(100);

  return (
    <div className="flex min-h-screen items-center justify-center p-4">
      <Card className="w-full max-w-md text-center">
        <CardHeader className="pb-4">
          <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-green-100">
            <CheckCircle className="h-8 w-8 text-green-600" />
          </div>
          <CardTitle className="text-2xl font-bold text-green-600">Congratulations!</CardTitle>
          <CardDescription className="text-base">
            Your supplier account is now fully set up and ready to go
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-6">
          <div className="rounded-lg border border-green-200 bg-green-50 p-4">
            <div className="mb-2 flex items-center gap-2">
              <Sparkles className="h-5 w-5 text-green-600" />
              <span className="font-semibold text-green-800">Your Siemens catalogue is live!</span>
            </div>
            <p className="text-sm text-green-700">
              312 products are now published and available to buyers
            </p>
          </div>

          <div className="space-y-3">
            <div className="flex justify-between text-sm">
              <span>Onboarding Progress</span>
              <span className="font-semibold">100%</span>
            </div>
            <Progress value={progress} className="h-2" />
          </div>

          <div className="text-muted-foreground space-y-2 text-sm">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span>Company profile completed</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span>Product catalogue uploaded</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span>Team setup completed</span>
            </div>
          </div>

          <Button onClick={() => setProgress(100)} className="w-full" size="lg">
            Go to Dashboard
          </Button>
        </CardContent>
      </Card>
    </div>
  );
  // }
}
