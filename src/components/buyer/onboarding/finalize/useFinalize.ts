'use client';

import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { OnboardingData } from './types';
import { useGetUserInfo } from '@/services/auth/useGetUserInfo';
import { FinalizeInfoResponse } from '@/types/finalizeResponse.types';
import { useFinalizeOnboarding } from '@/services/buyer/useFinalizeOnboarding';
import { useSourcingIntentManagement } from '../../sourcing-intent-management/display-and-edit/useSourcingIntentManagement';

function mapApiToOnboardingData(apiData: FinalizeInfoResponse): OnboardingData {
  return {
    accountbasics_info: {
      full_name: apiData.buyer_details.display_name,
      email: apiData.email,
      phone_number: apiData.phone,
    },
    jurisdiction: {
      country: apiData.jurisdiction?.country?.name || '',
      state: apiData.jurisdiction?.state?.name || '',
    },
    organization_info: {
      org_type: apiData.organisation_type.name || '',
      org_name: apiData.buyer_details.display_name,
      registration_number: '',
      tax_id: '',
      business_address: '',
    },
    sourcing_intent: {
      sourcing_for: [],
      sourcing_from: [],
      use_case: [],
      product_categories: [],
      compliance_reqs: [],
      pref_shipping_term: '',
      target_lead_time: '',
    },
    team_members: [],
    kyc_status: 'PENDING',
  };
}

export const useReviewOnboarding = () => {
  const router = useRouter();
  const { data: buyerData, isLoading: isBuyerLoading } = useGetUserInfo('buyer');

  const { displayData: sourcingIntentData, isLoading: isSourcingLoading } =
    useSourcingIntentManagement({
      userType: 'buyer',
    });

  const { mutate: finaliseSubmit, isPending } = useFinalizeOnboarding({
    onSuccess(data) {
      toast.success(data.message);
      router.push('/buyer/dashboard');
    },
  });

  // Combine all data
  const onboardingData = buyerData
    ? {
        ...mapApiToOnboardingData(buyerData),
        sourcing_intent: sourcingIntentData || {
          sourcing_for: [],
          sourcing_from: [],
          use_case: [],
          product_categories: [],
          compliance_reqs: [],
          pref_shipping_term: '',
          target_lead_time: '',
        },
      }
    : null;

  const isLoading = isBuyerLoading || isSourcingLoading;

  const handleConfirmFinish = () => finaliseSubmit('buyer');

  return {
    onboardingData,
    isLoading,
    isSubmitting: isPending,
    handleConfirmFinish,
  };
};
