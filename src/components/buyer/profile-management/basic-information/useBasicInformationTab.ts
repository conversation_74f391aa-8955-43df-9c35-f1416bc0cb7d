import { AxiosError } from 'axios';
import { toast } from 'sonner';
import { useEffect, useState } from 'react';
import { BuyerInfoResponse } from '@/types/newBuyer.types';
import { useGetBuyerInfo } from '@/services/buyer/useGetBuyerInformation';
import { useUpdateBuyerInfo } from '@/services/buyer/useUpdateBuyerInformation';

const useBasicInformationTab = () => {
  const [buyerInfo, setBuyerInfo] = useState<{
    displayName: string;
    email: string;
    phone: string;
    country: string;
    state: string;
    organisationType: string;
  } | null>(null);

  const handleSuccess = (data: BuyerInfoResponse) => {
    const transformed = {
      displayName: data.buyer_details?.display_name ?? '',
      email: data.email,
      phone: data.phone,
      country: data.jurisdiction?.country?.name ?? '',
      state: data.jurisdiction?.state?.name ?? '',
      organisationType: data.organisation_type?.name ?? '',
    };

    setBuyerInfo(transformed);
  };

  const handleError = (error: AxiosError) => {
    const message = error instanceof Error ? error.message : 'An unknown error occurred.';
    toast.error(message);
  };

  function handleUpdateSuccess() {
    toast.success('Information updated successfully!');
  }

  const { mutateAsync: getBuyerInfo, isPending } = useGetBuyerInfo({
    onSuccess: handleSuccess,
    onError: handleError,
  });

  const { mutateAsync: saveUpdateBuyerInfo, isPending: isUpdating } = useUpdateBuyerInfo({
    onSuccess: handleUpdateSuccess,
    onError: handleError,
  });

  const updateBuyerInfoDetails = (key: string, name: string) => {
    setBuyerInfo((prev) => (prev ? { ...prev, [key]: name } : prev));
  };

  useEffect(() => {
    getBuyerInfo();
  }, []);

  return {
    isPending,
    isUpdating,
    buyerInfo,
    saveUpdateBuyerInfo,
    updateBuyerInfoDetails,
  };
};

export default useBasicInformationTab;
