import { zodResolver } from '@hookform/resolvers/zod';
import { useFieldArray, useForm } from 'react-hook-form';
import z from 'zod';

export const seatRoleOptions = ['CATALOG MANAGER', 'SALES REP', 'FINANCE', 'SUPPORT'] as const;

const FormSchema = z.object({
  team: z
    .array(
      z.object({
        fullName: z.string().min(2, {
          message: 'Name must be at least 2 characters.',
        }),
        phone: z.string().min(8, 'Phone is required'),
        email: z.string().email('Invalid email'),
        seat_role: z.enum(seatRoleOptions, {
          errorMap: () => ({ message: 'Please select a valid seat role' }),
        }),
      }),
    )
    .min(1, 'You have to invite at least one team member'),
});

type FormValues = z.infer<typeof FormSchema>;

export const useInviteTeam = () => {
  const form = useForm<FormValues>({
    resolver: zod<PERSON><PERSON><PERSON>ver(FormSchema),
    defaultValues: {
      team: [
        {
          fullName: '',
          phone: '',
          email: '',
          seat_role: 'CATALOG MANAGER',
        },
      ],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'team',
  });

  function onSubmit(data: FormValues) {
    console.table(data.team);
  }

  return {
    form,
    onSubmit,
    fields,
    append,
    remove,
  };
};
