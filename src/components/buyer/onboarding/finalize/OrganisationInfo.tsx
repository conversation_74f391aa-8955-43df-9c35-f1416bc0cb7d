import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from '@kratex-tradetech/kratex-ui-library';
import { Building } from 'lucide-react';

interface OrganizationInfoData {
  org_type: string;
  org_name: string;
  registration_number: string;
  tax_id: string;
  business_address: string;
}

interface OrganizationInfoProps {
  data: OrganizationInfoData;
}

const OrganizationInfo: React.FC<OrganizationInfoProps> = ({ data }) => {
  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Building className="h-4 w-4" />
            Organization Information
          </CardTitle>
        </div>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <div>
            <p className="text-sm font-medium text-gray-600">Organization Type</p>
            <p className="text-sm">{data.org_type}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-600">Organization Name</p>
            <p className="text-sm">{data.org_name}</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default OrganizationInfo;
