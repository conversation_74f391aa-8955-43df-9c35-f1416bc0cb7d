import { useGetFormDefinition } from '@/services/forms/useGetFormDefinition';
import { SubmitBusinessDetailsPayload } from '@/services/supplier/types';
import { useSubmitBusinessDetails } from '@/services/supplier/useSubmitBusinessDetails';
import { newSupplierStore } from '@/store/newSupplier.store';
import { FormioSchema } from '@/utils/formio';
import { injectPhoneNumberIntoSchema } from '@/utils/formio/formSchemaDataInjection';
import { useEffect, useMemo } from 'react';
import { toast } from 'sonner';

interface BusinessDetailsFormData {
  legal_business_name: string;
  registration_number: string;
  address_line1: string;
  address_line2?: string;
  city: string;
  postcode: string;
  primary_contact_name: string;
  phone: string;
  support_email: string;
}

const useBusinessDetails = () => {
  const {
    newSupplierData: { phoneNo, jurisdiction, orgType },
  } = newSupplierStore();

  const {
    data: businessDetails,
    isError,
    isLoading,
  } = useGetFormDefinition('businessDetailsSupplierIndia');

  useEffect(() => {
    if (isError) {
      toast.error('Error loading the business details form, please try again.');
    }
  }, [isError]);

  const { mutate, isPending } = useSubmitBusinessDetails({
    onSuccess: () => toast.success('Business details submitted successfully!'),
    onError: (error) =>
      toast.error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          'Failed to submit business details!',
      ),
  });

  const handleSubmit = (data: BusinessDetailsFormData) => {
    const payload: SubmitBusinessDetailsPayload = {
      email: data.support_email, // user email
      name: data.primary_contact_name, // user name
      country_of_origin: jurisdiction.countryOfOrigin.iso2, // confirmed with backend for iso2
      organisation_type: orgType.map((item) => item.id),
      countries_to_serve: jurisdiction.countriesToServe.map((item) => item.id),
      supplier_details: data,
    };
    mutate(payload);
  };

  const transformedSchema = useMemo(() => {
    return businessDetails?.schema
      ? injectPhoneNumberIntoSchema(businessDetails.schema as FormioSchema, phoneNo)
      : undefined;
  }, [businessDetails?.schema, phoneNo]);

  return {
    handleSubmit,
    transformedSchema,
    isButtonDisabled: isPending,
    isFormLoading: isLoading,
    isFormLoadError: isError,
  };
};

export default useBusinessDetails;
