'use client';

import { useGetFormDefinition } from '@/services/forms/useGetFormDefinition';
import {
  newBuyerStore,
  useNewBuyerJurisdiction,
  useNewBuyerOrgType,
  useNewBuyerPhoneNo,
} from '@/store/newBuyer.store';
import { userStore } from '@/store/user.store';
import { injectPhoneNumberIntoSchema } from '@/utils/formio/formSchemaDataInjection';
import { useRouter } from 'next/navigation';
import { useMemo, useState } from 'react';
import { toast } from 'sonner';
import { AxiosError } from 'axios';
import { useBuyerRegister } from '@/services/buyer/useRegisterBuyer';
import { BuyerRegistrationResponse } from './types';

export const useAccountBasics = () => {
  const router = useRouter();
  const { setUser } = userStore.getState();
  const {
    data: formSchema,
    isLoading: schemaLoading,
    error: schemaError,
  } = useGetFormDefinition('accountBasicsBuyer');

  const [isSubmitting, setIsSubmitting] = useState(false);
  const { mutateAsync: registerBuyer, isPending } = useBuyerRegister({
    onSuccess: handleSubmitSuccess,
    onSettled: handleSubmitSettled,
    onError: handleSubmitError,
  });
  const jurisdiction = useNewBuyerJurisdiction();
  const orgType = useNewBuyerOrgType();
  const { setAccountBasics } = newBuyerStore();
  const phoneNumber = useNewBuyerPhoneNo();

  const { newBuyerData } = newBuyerStore();
  const isEmailVerified = newBuyerData?.isEmailVerified;

  // Transform the schema to make phone field disabled and pre-populated
  const transformedSchema = useMemo(() => {
    return formSchema?.schema
      ? injectPhoneNumberIntoSchema(formSchema.schema, phoneNumber)
      : undefined;
  }, [formSchema?.schema, phoneNumber]);

  const handleFormSubmit = async (formData: Record<string, unknown>) => {
    const formDataWithPhone = {
      ...formData,
      phone: phoneNumber,
    };

    setAccountBasics(formDataWithPhone);
    setIsSubmitting(true);

    if (!isEmailVerified) {
      toast.error('Please verify your email address before submitting the form.');
      setIsSubmitting(false);
      return;
    }

    const payload = {
      organisation_type: orgType.id,
      jurisdiction: jurisdiction.state.id,
      email: formData.email,
      name: formData.display_name,
      buyer_details: formDataWithPhone,
    };
    await registerBuyer(payload);
  };

  // TODO: discuss with backend and maybe update the response here
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  function handleSubmitSuccess(data: BuyerRegistrationResponse) {
    // TODO: this must be updated later once backend is updated
    const formData = { display_name: 'Buyer', email: '<EMAIL>' };

    const displayName = formData?.display_name?.toString() ?? 'Buyer';
    const email = formData?.email?.toString() ?? '<EMAIL>';
    setUser({
      role: 2,
      GivenName: displayName,
      Surname: displayName,
      Email: email,
      isRegistered: true,
    });
    toast.success('Account created successfully!');
    router.push(`/buyer/kyc/`);
  }

  function handleSubmitError(error: AxiosError) {
    const message = error instanceof Error ? error.message : 'An unknown error occurred.';
    toast.error(message);
  }

  function handleSubmitSettled() {
    setIsSubmitting(false);
  }

  return {
    formSchema: transformedSchema,
    isEmailVerified,
    schemaLoading,
    schemaError,
    isSubmitting,
    handleFormSubmit,
    isButtonDisabled: isPending,
  };
};
