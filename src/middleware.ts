import { NextRequest, NextResponse } from 'next/server';

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Get token from cookies
  const token = request.cookies.get('token')?.value;

  // Define paths that don't require authentication
  const isAuthRoute = pathname.startsWith('/login-signup');
  const isOnboardingRoute = pathname.startsWith('/buyer') || pathname.startsWith('/supplier');

  // Allow access to auth and onboarding routes without token
  if (isAuthRoute || isOnboardingRoute) {
    return NextResponse.next();
  }

  // If no token and trying to access protected routes, redirect to login
  if (!token) {
    const loginUrl = new URL('/login-signup', request.url);
    return NextResponse.redirect(loginUrl);
  }

  // If token exists, allow access
  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files (images, icons, etc.)
     */
    '/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
};