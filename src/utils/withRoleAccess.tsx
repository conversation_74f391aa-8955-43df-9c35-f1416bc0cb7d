'use client';

import { ComponentType, useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { RoleEnum, User } from '@/types/user.types';
import { userStore } from '@/store/user.store';
import { clearTokenFromCookie, getTokenFromCookie } from '@/lib/cookies';

interface AuthState {
  isAuthenticated: boolean;
  isLoading: boolean;
  user: User | null;
  token: string | null;
}

// User roles enum for better type safety

// HOC options interface
interface ProtectedRouteOptions {
  allowedRoles?: RoleEnum[]; // Specific roles that can access this route
  requireAuth?: boolean; // Whether authentication is required (default: true)
  redirectTo?: string; // Custom redirect path (default: /login)
  fallbackComponent?: ComponentType; // Custom loading/error component
  returnUrl?: boolean; // Whether to include return URL in redirect (default: true)
}

/**
 * Utility function to check if JWT token is valid
 */
const checkTokenValidity = (token: string): boolean => {
  if (!token) return false;
  try {
    const payload = JSON.parse(atob(token.split('.')[1]));
    const currentTime = Math.floor(Date.now() / 1000);
    if (payload.exp && payload.exp < currentTime) {
      console.warn('Token has expired');
      return false;
    }
    return true;
  } catch (error) {
    console.error('Invalid token format:', error);
    return false;
  }
};

/**
 * Hook to get current authentication state
 */
export const useAuthState = (): AuthState => {
  const router = useRouter();
  const [authState, setAuthState] = useState<AuthState>({
    isAuthenticated: false,
    isLoading: true,
    user: null,
    token: null,
  });

  const validateAndSetAuth = (tokenToValidate: string | null): boolean => {
    if (!tokenToValidate) {
      setAuthState({
        isAuthenticated: false,
        isLoading: false,
        user: null,
        token: null,
      });
      return false;
    }

    const isValid = checkTokenValidity(tokenToValidate);
    if (isValid) {
      try {
        const payload = JSON.parse(atob(tokenToValidate.split('.')[1]));
        setAuthState({
          isAuthenticated: true,
          isLoading: false,
          user: payload,
          token: tokenToValidate,
        });
        return true;
      } catch (error) {
        console.error('Error decoding token:', error);
        if (typeof window !== 'undefined') {
          clearTokenFromCookie();
        }
        setAuthState({
          isAuthenticated: false,
          isLoading: false,
          user: null,
          token: null,
        });
        return false;
      }
    } else {
      if (typeof window !== 'undefined') {
        clearTokenFromCookie();
      }
      setAuthState({
        isAuthenticated: false,
        isLoading: false,
        user: null,
        token: null,
      });
      return false;
    }
  };

  useEffect(() => {
    // Only run on client side
    if (typeof window !== 'undefined') {
      const storedToken = getTokenFromCookie();
      if (storedToken) {
        const result = validateAndSetAuth(storedToken);
        if (!result) {
          router.replace('/login-signup');
        }
      }
    }
  }, [router]);

  useEffect(() => {
    // Only add event listener on client side
    if (typeof window !== 'undefined') {
      const handleStorageChange = (e: StorageEvent) => {
        if (e.key === 'token') {
          validateAndSetAuth(e.newValue);
        }
      };

      window.addEventListener('storage', handleStorageChange);
      return () => window.removeEventListener('storage', handleStorageChange);
    }
  }, []);

  return authState;
};

/**
 * Default loading component
 */
const DefaultLoadingComponent: React.FC = () => (
  <div className="flex min-h-screen items-center justify-center">
    <div className="h-12 w-12 animate-spin rounded-full border-b-2 border-black"></div>
  </div>
);

/**
 * Default unauthorized component
 */
const DefaultUnauthorizedComponent: React.FC = () => (
  <div className="flex min-h-screen items-center justify-center">
    <div className="text-center">
      <h1 className="mb-2 text-2xl font-bold text-red-600">Access Denied</h1>
      <p className="text-gray-600">You don&#39;t have permission to access this page.</p>
    </div>
  </div>
);

/**
 * Higher Order Component for protected routes
 * @param WrappedComponent - The component to protect
 * @param options - Configuration options for the protection
 * @returns Protected component
 */
export const withProtectedRoute = <P extends object>(
  WrappedComponent: ComponentType<P>,
  options: ProtectedRouteOptions = {},
) => {
  const {
    allowedRoles,
    requireAuth = true,
    redirectTo = '/login-signup',
    fallbackComponent: FallbackComponent = DefaultLoadingComponent,
    returnUrl = true,
  } = options;

  const ProtectedComponent: React.FC<P> = (props) => {
    const { isAuthenticated, isLoading } = useAuthState();
    const { user } = userStore();
    const router = useRouter();
    const [isRedirecting, setIsRedirecting] = useState(false);

    // Handle redirect when authentication is required but user is not authenticated
    useEffect(() => {
      if (!isLoading && requireAuth && !isAuthenticated && !isRedirecting) {
        setIsRedirecting(true);
        // Redirect to login with return URL
        if (returnUrl) {
          const currentPath = typeof window !== 'undefined' ? window.location.pathname : '';
          const returnUrlParam = encodeURIComponent(currentPath);
          console.log(
            `redirecting to ${redirectTo} and returnUrl is ${returnUrlParam} from the hoc`,
          );
          router.replace(`${redirectTo}?returnUrl=${returnUrlParam}`);
        } else {
          console.log(`redirecting to ${redirectTo} from the hoc`);
          router.replace(redirectTo);
        }
      }
    }, [isLoading, isAuthenticated, requireAuth, isRedirecting, router, redirectTo, returnUrl]);

    // Show loading component while checking authentication or redirecting
    if (isLoading || isRedirecting) {
      return <FallbackComponent />;
    }

    // If authentication is required but user is not authenticated (fallback)
    if (requireAuth && !isAuthenticated) {
      return <FallbackComponent />;
    }

    // If specific roles are required, check if user has the required role
    if (allowedRoles && allowedRoles.length > 0 && !user?.isRegistered) {
      const hasRequiredRole = allowedRoles.includes(user?.role as RoleEnum);
      if (!hasRequiredRole) {
        return <DefaultUnauthorizedComponent />;
      }
    }

    // If user is authenticated but doesn't have required role and no specific roles are set
    if (requireAuth && allowedRoles && allowedRoles.length > 0 && !user?.isRegistered) {
      return <DefaultUnauthorizedComponent />;
    }

    // If all checks pass, render the wrapped component with auth data
    return <WrappedComponent {...props} user={user} isAuthenticated={isAuthenticated} />;
  };

  // Set display name for better debugging
  ProtectedComponent.displayName = `withProtectedRoute(${WrappedComponent.displayName || WrappedComponent.name})`;

  return ProtectedComponent;
};

/**
 * Convenience HOCs for specific roles
 */
export const withAdminRoute = <P extends object>(component: ComponentType<P>) =>
  withProtectedRoute(component, { allowedRoles: [RoleEnum.ADMIN] });

export const withSupplierRoute = <P extends object>(component: ComponentType<P>) =>
  withProtectedRoute(component, { allowedRoles: [RoleEnum.SUPPLIER] });

export const withBuyerRoute = <P extends object>(component: ComponentType<P>) =>
  withProtectedRoute(component, { allowedRoles: [RoleEnum.BUYER] });

/**
 * HOC for routes accessible by multiple roles
 */
export const withMultiRoleRoute = <P extends object>(
  component: ComponentType<P>,
  roles: RoleEnum[],
) => withProtectedRoute(component, { allowedRoles: roles });

/**
 * HOC for public routes (no authentication required)
 */
export const withPublicRoute = <P extends object>(component: ComponentType<P>) =>
  withProtectedRoute(component, { requireAuth: false });

/**
 * Server-side protection for getServerSideProps
 * Use this in your page's getServerSideProps to protect server-side rendering
 */
export const protectServerSideProps = (allowedRoles?: RoleEnum[], redirectTo = '/login-signup') => {
  return (context: any) => {
    // Get token from cookies or headers
    const token =
      context.req.cookies.token || context.req.headers.authorization?.replace('Bearer ', '');

    if (!token || !checkTokenValidity(token)) {
      return {
        redirect: {
          destination: redirectTo,
          permanent: false,
        },
      };
    }

    // Check roles if specified
    if (allowedRoles && allowedRoles.length > 0) {
      try {
        const payload = JSON.parse(atob(token.split('.')[1]));
        const hasRequiredRole = allowedRoles.includes(payload.role as RoleEnum);

        if (!hasRequiredRole) {
          return {
            redirect: {
              destination: '/unauthorized',
              permanent: false,
            },
          };
        }
      } catch (error) {
        console.error(error);
        return {
          redirect: {
            destination: redirectTo,
            permanent: false,
          },
        };
      }
    }

    return {
      props: {}, // Will be passed to the page component as props
    };
  };
};

// Usage Examples:

/*
// Basic protected route (requires authentication)
const ProtectedDashboard = withProtectedRoute(Dashboard);

// Admin only route
const AdminPanel = withAdminRoute(AdminPanelComponent);

// Supplier only route  
const SupplierDashboard = withSupplierRoute(SupplierDashboardComponent);

// Buyer only route
const BuyerDashboard = withBuyerRoute(BuyerDashboardComponent);

// Multiple roles allowed
const SharedReports = withMultiRoleRoute(ReportsComponent, [RoleEnum.ADMIN, RoleEnum.SUPPLIER]);

// Public route (no auth required)
const PublicAbout = withPublicRoute(AboutComponent);

// Custom options
const CustomProtected = withProtectedRoute(SomeComponent, {
  allowedRoles: [RoleEnum.ADMIN],
  redirectTo: '/unauthorized',
  fallbackComponent: CustomLoadingSpinner,
  returnUrl: false
});

// In your pages:
// pages/dashboard.tsx
export default withProtectedRoute(DashboardComponent);

// pages/admin/index.tsx
export default withAdminRoute(AdminComponent);

// pages/supplier/dashboard.tsx
export default withSupplierRoute(SupplierDashboardComponent);

// pages/buyer/dashboard.tsx
export default withBuyerRoute(BuyerDashboardComponent);

// For server-side protection:
// pages/admin/users.tsx
export const getServerSideProps = protectServerSideProps([RoleEnum.ADMIN]);
export default withAdminRoute(AdminUsersComponent);

// For pages with both client and server protection:
// pages/secure-data.tsx
export const getServerSideProps = protectServerSideProps([RoleEnum.ADMIN, RoleEnum.SUPPLIER]);
export default withMultiRoleRoute(SecureDataComponent, [RoleEnum.ADMIN, RoleEnum.SUPPLIER]);
*/
