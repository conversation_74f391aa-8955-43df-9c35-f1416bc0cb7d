import { apiUrls } from '@/constants/apiUrls';
import apiClient from '@/lib/api-client';
import { UseMutationHookProps } from '@/types/api';
import { UserRole } from '@/types/user.types';
import { useMutation } from '@tanstack/react-query';
import { AxiosError } from 'axios';

async function finalizeAndSubmit(userRole: UserRole) {
  const response = await apiClient.post<{ message: string }>(
    apiUrls.users.completeOnboarding(userRole),
  );
  return response.data;
}

export const useFinalizeOnboarding = ({
  onSettled,
  onError,
  onSuccess,
}: UseMutationHookProps<{ message: string }, unknown>) => {
  return useMutation<{ message: string }, AxiosError<unknown>, UserRole>({
    mutationFn: (userRole) => finalizeAndSubmit(userRole),
    onSettled,
    onError,
    onSuccess,
  });
};
