/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  AccountBasics,
  Country,
  Jurisdiction,
  NewBuyerData,
  OrgType,
  PhoneNo,
  State,
} from '@/types/newBuyer.types';
import { RoleEnum } from '@/types/user.types';
import { createPersistedStore } from '@/utils/zustand/createPersistedStore';

interface NewBuyerStore {
  newBuyerData: NewBuyerData;
  setNewBuyerData: (data: NewBuyerData) => void;
  updateNewBuyerData: (data: Partial<NewBuyerData>) => void;
  clearNewBuyerData: () => void;
  resetToDefault: () => void;

  // Individual field setters for convenience
  setPhoneNo: (phoneNo: PhoneNo) => void;
  setRole: (role: RoleEnum) => void;
  setCountry: (country: Country) => void;
  setState: (state: State) => void;
  setJurisdiction: (jurisdiction: Jurisdiction) => void;
  setOrgType: (orgType: OrgType) => void;
  setAccountBasics: (accountBasics: AccountBasics) => void;
  setEmailVerified: (verified: boolean) => void;

  // Computed getters for type safety
  getNewBuyerData: () => NewBuyerData;
  isDataComplete: () => boolean;
  hasValidPhoneNo: () => boolean;
  hasValidCountry: () => boolean;
  hasValidState: () => boolean;
  hasValidRole: () => boolean;
  hasValidOrgType: () => boolean;
  hasValidJurisdiction: () => boolean;
  hasValidAccountBasics: () => boolean;
}

// Type-safe creation of default data
// Update your store's default data to match the full Country type
const createDefaultNewBuyerData = (): NewBuyerData => ({
  phoneNo: '' as PhoneNo,
  country: {
    id: 0,
    name: '',
    emoji: '',
    alpha2: '',
    alpha3: '',
    countryCallingCodes: [],
    ioc: '',
    iso2: '',
    iso3: '',
  } as Country,
  state: { id: 0, name: '', iso2: '', state_code: '' } as State,
  jurisdiction: {
    country: {
      id: 0,
      name: '',
      emoji: '',
      alpha2: '',
      alpha3: '',
      iso2: '',
      iso3: '',
      countryCallingCodes: [],
      ioc: '',
    } as Country,
    state: { id: 0, name: '', iso2: '', state_code: '' } as State,
  } as Jurisdiction,
  orgType: { id: 0, name: '' } as OrgType,
  accountBasics: {} as AccountBasics,
  isEmailVerified: false,
  role: 2 as RoleEnum,
});

const defaultNewBuyerData: NewBuyerData = createDefaultNewBuyerData();

// Enhanced type guard functions with better type safety
const isValidPhoneNo = (phoneNo: PhoneNo): phoneNo is NonNullable<PhoneNo> => {
  return typeof phoneNo === 'string' && phoneNo.trim().length > 0;
};

const isValidCountry = (country: Country): country is Required<Country> => {
  return (
    typeof country === 'object' &&
    country !== null &&
    typeof country.name === 'string' &&
    country.name.trim().length > 0
  );
};

const isValidState = (state: State): state is Required<State> => {
  return (
    typeof state === 'object' &&
    state !== null &&
    typeof state.id === 'number' &&
    state.id > 0 &&
    typeof state.name === 'string' &&
    state.name.trim().length > 0
  );
};

const isValidRole = (role: RoleEnum): role is Required<RoleEnum> => {
  return role !== null && typeof role === 'number' && role > 0;
};

const isValidOrgType = (orgType: OrgType): orgType is Required<OrgType> => {
  return (
    typeof orgType === 'object' &&
    orgType !== null &&
    typeof orgType.id === 'number' &&
    orgType.id > 0 &&
    typeof orgType.name === 'string' &&
    orgType.name.trim().length > 0
  );
};

const isValidJurisdiction = (
  jurisdiction: Jurisdiction,
): jurisdiction is Required<Jurisdiction> => {
  return (
    typeof jurisdiction === 'object' &&
    jurisdiction !== null &&
    isValidCountry(jurisdiction.country) &&
    isValidState(jurisdiction.state)
  );
};

const isValidAccountBasics = (
  accountBasics: AccountBasics,
): accountBasics is Required<AccountBasics> => {
  return (
    typeof accountBasics === 'object' &&
    accountBasics !== null &&
    Object.keys(accountBasics).length > 0
  );
};

// Type-safe deep merge utility
const deepMerge = <T extends Record<string, any>>(target: T, source: Partial<T>): T => {
  const result = { ...target };

  for (const key in source) {
    if (source.hasOwnProperty(key)) {
      const sourceValue = source[key];
      const targetValue = result[key];

      if (
        sourceValue !== null &&
        typeof sourceValue === 'object' &&
        !Array.isArray(sourceValue) &&
        targetValue !== null &&
        typeof targetValue === 'object' &&
        !Array.isArray(targetValue)
      ) {
        result[key] = deepMerge(targetValue, sourceValue);
      } else if (sourceValue !== undefined) {
        result[key] = sourceValue as T[Extract<keyof T, string>];
      }
    }
  }

  return result;
};

// Helper function to safely merge partial data with enhanced type safety
const safeMergeNewBuyerData = (
  current: NewBuyerData,
  updates: Partial<NewBuyerData>,
): NewBuyerData => {
  // Use deep merge for better type safety
  const merged = deepMerge(current, updates);

  // Ensure jurisdiction consistency
  if (updates.jurisdiction) {
    merged.jurisdiction = {
      country: updates.jurisdiction.country
        ? deepMerge(current.jurisdiction.country, updates.jurisdiction.country)
        : current.jurisdiction.country,
      state: updates.jurisdiction.state
        ? deepMerge(current.jurisdiction.state, updates.jurisdiction.state)
        : current.jurisdiction.state,
    };
  }

  return merged;
};

// Helper function to create a complete NewBuyerData from partial data
const createCompleteNewBuyerData = (partialData: Partial<NewBuyerData>): NewBuyerData => {
  return safeMergeNewBuyerData(createDefaultNewBuyerData(), partialData);
};

// Type-safe serialization helpers
const serializeState = (state: Pick<NewBuyerStore, 'newBuyerData'>): string => {
  try {
    return JSON.stringify(state);
  } catch (error) {
    console.error('Failed to serialize newBuyerStore state:', error);
    return JSON.stringify({ newBuyerData: defaultNewBuyerData });
  }
};

const deserializeState = (str: string): Pick<NewBuyerStore, 'newBuyerData'> => {
  try {
    const parsed: unknown = JSON.parse(str);

    // Type guard for parsed data
    if (parsed && typeof parsed === 'object' && parsed !== null && 'newBuyerData' in parsed) {
      const typedParsed = parsed as { newBuyerData: Partial<NewBuyerData> };
      return {
        newBuyerData: safeMergeNewBuyerData(defaultNewBuyerData, typedParsed.newBuyerData),
      };
    }

    return { newBuyerData: defaultNewBuyerData };
  } catch (error) {
    console.error('Failed to deserialize newBuyerStore state:', error);
    return { newBuyerData: defaultNewBuyerData };
  }
};

// Migration function with proper typing
const migrateState = (
  persistedState: unknown,
  version: number,
): Pick<NewBuyerStore, 'newBuyerData'> => {
  if (version === 0) {
    // Handle migration from version 0 to 1
    if (
      persistedState &&
      typeof persistedState === 'object' &&
      persistedState !== null &&
      'newBuyerData' in persistedState
    ) {
      const typedState = persistedState as { newBuyerData?: Partial<NewBuyerData> };
      return {
        newBuyerData: safeMergeNewBuyerData(defaultNewBuyerData, typedState.newBuyerData || {}),
      };
    }
  }

  // Fallback for unknown versions or invalid data
  if (
    persistedState &&
    typeof persistedState === 'object' &&
    persistedState !== null &&
    'newBuyerData' in persistedState
  ) {
    return persistedState as Pick<NewBuyerStore, 'newBuyerData'>;
  }

  return { newBuyerData: defaultNewBuyerData };
};

export const newBuyerStore = createPersistedStore<NewBuyerStore>('new-buyer-store', (set, get) => ({
  newBuyerData: defaultNewBuyerData,

  setNewBuyerData: (data: NewBuyerData) => {
    set({ newBuyerData: data });
  },

  updateNewBuyerData: (data: Partial<NewBuyerData>) => {
    set((state: { newBuyerData: NewBuyerData }) => ({
      newBuyerData: safeMergeNewBuyerData(state.newBuyerData, data),
    }));
  },

  clearNewBuyerData: () => {
    set({ newBuyerData: createDefaultNewBuyerData() });
  },

  resetToDefault: () => {
    set({ newBuyerData: defaultNewBuyerData });
  },

  // Individual field setters with enhanced type safety
  setPhoneNo: (phoneNo: PhoneNo) => {
    set((state: { newBuyerData: any }) => ({
      newBuyerData: {
        ...state.newBuyerData,
        phoneNo,
      },
    }));
  },

  setRole: (role: RoleEnum) => {
    if (!isValidRole(role)) {
      console.warn('Invalid role provided to setRole:', role);
      return;
    }
    set((state: { newBuyerData: any }) => ({
      newBuyerData: {
        ...state.newBuyerData,
        role,
      },
    }));
  },

  setEmailVerified: (verified: boolean) =>
    set(
      (state: {
        newBuyerData: {
          isEmailVerified: boolean;
        };
      }) => ({
        newBuyerData: {
          ...state.newBuyerData,
          isEmailVerified: verified,
        },
      }),
    ),

  setCountry: (country: Country) => {
    set(
      (state: {
        newBuyerData: {
          country: { alpha2: string };
          state: any;
          jurisdiction: { country: any; state: any };
        };
      }) => {
        const shouldResetState = country.alpha2 !== state.newBuyerData.country.alpha2;
        return {
          newBuyerData: {
            ...state.newBuyerData,
            country,
            // Reset state when country changes to maintain consistency
            state: shouldResetState ? ({ id: 0, name: '' } as State) : state.newBuyerData.state,
            // Also update jurisdiction country if it was linked
            jurisdiction: {
              ...state.newBuyerData.jurisdiction,
              country: shouldResetState ? country : state.newBuyerData.jurisdiction.country,
              state: shouldResetState
                ? ({ id: 0, name: '', iso2: '', state_code: '' } as State)
                : state.newBuyerData.jurisdiction.state,
            },
          },
        };
      },
    );
  },

  setState: (newState: State) => {
    set((state: { newBuyerData: any }) => ({
      newBuyerData: {
        ...state.newBuyerData,
        state: newState,
      },
    }));
  },

  setJurisdiction: (jurisdiction: Jurisdiction) => {
    set((state: { newBuyerData: any }) => ({
      newBuyerData: {
        ...state.newBuyerData,
        jurisdiction: {
          country: { ...jurisdiction.country },
          state: { ...jurisdiction.state },
        },
      },
    }));
  },

  setOrgType: (orgType: OrgType) => {
    set((state: { newBuyerData: any }) => ({
      newBuyerData: {
        ...state.newBuyerData,
        orgType,
      },
    }));
  },

  setAccountBasics: (accountBasics: AccountBasics) => {
    set((state: { newBuyerData: { accountBasics: any } }) => ({
      newBuyerData: {
        ...state.newBuyerData,
        accountBasics: {
          ...state.newBuyerData.accountBasics,
          ...accountBasics,
        },
      },
    }));
  },

  // Enhanced computed getters for type safety
  getNewBuyerData: (): NewBuyerData => {
    return get().newBuyerData;
  },

  isDataComplete: (): boolean => {
    const data = get().newBuyerData;
    return (
      isValidPhoneNo(data.phoneNo) &&
      isValidCountry(data.country) &&
      isValidState(data.state) &&
      isValidRole(data.role) &&
      isValidOrgType(data.orgType) &&
      isValidJurisdiction(data.jurisdiction) &&
      isValidAccountBasics(data.accountBasics)
    );
  },

  hasValidPhoneNo: (): boolean => {
    return isValidPhoneNo(get().newBuyerData.phoneNo);
  },

  hasValidCountry: (): boolean => {
    return isValidCountry(get().newBuyerData.country);
  },

  hasValidState: (): boolean => {
    return isValidState(get().newBuyerData.state);
  },

  hasValidRole: (): boolean => {
    return isValidRole(get().newBuyerData.role);
  },

  hasValidOrgType: (): boolean => {
    return isValidOrgType(get().newBuyerData.orgType);
  },

  hasValidJurisdiction: (): boolean => {
    return isValidJurisdiction(get().newBuyerData.jurisdiction);
  },

  hasValidAccountBasics: (): boolean => {
    return isValidAccountBasics(get().newBuyerData.accountBasics);
  },
}));

// Export typed hooks for better developer experience with enhanced type safety
export const useNewBuyerData = () => newBuyerStore((state) => state.newBuyerData);

export const useNewBuyerActions = () =>
  newBuyerStore((state) => ({
    setNewBuyerData: state.setNewBuyerData,
    updateNewBuyerData: state.updateNewBuyerData,
    clearNewBuyerData: state.clearNewBuyerData,
    resetToDefault: state.resetToDefault,
    setPhoneNo: state.setPhoneNo,
    setRole: state.setRole,
    setCountry: state.setCountry,
    setState: state.setState,
    setJurisdiction: state.setJurisdiction,
    setOrgType: state.setOrgType,
    setAccountBasics: state.setAccountBasics,
    setEmailVerified: state.setEmailVerified,
  }));

export const useNewBuyerValidation = () =>
  newBuyerStore((state) => ({
    isDataComplete: state.isDataComplete,
    hasValidPhoneNo: state.hasValidPhoneNo,
    hasValidCountry: state.hasValidCountry,
    hasValidState: state.hasValidState,
    hasValidRole: state.hasValidRole,
    hasValidOrgType: state.hasValidOrgType,
    hasValidJurisdiction: state.hasValidJurisdiction,
    hasValidAccountBasics: state.hasValidAccountBasics,
  }));

// Type-safe selectors for specific data pieces
export const useNewBuyerPhoneNo = () => newBuyerStore((state) => state.newBuyerData.phoneNo);
export const useNewBuyerCountry = () => newBuyerStore((state) => state.newBuyerData.country);
export const useNewBuyerState = () => newBuyerStore((state) => state.newBuyerData.state);
export const useNewBuyerRole = () => newBuyerStore((state) => state.newBuyerData.role);
export const useNewBuyerOrgType = () => newBuyerStore((state) => state.newBuyerData.orgType);
export const useNewBuyerJurisdiction = () =>
  newBuyerStore((state) => state.newBuyerData.jurisdiction);
export const useNewBuyerAccountBasics = () =>
  newBuyerStore((state) => state.newBuyerData.accountBasics);

// Type-safe computed selectors
export const useIsNewBuyerDataComplete = () => newBuyerStore((state) => state.isDataComplete());
export const useNewBuyerValidationStatus = () =>
  newBuyerStore((state) => ({
    phoneNo: state.hasValidPhoneNo(),
    country: state.hasValidCountry(),
    state: state.hasValidState(),
    role: state.hasValidRole(),
    orgType: state.hasValidOrgType(),
    jurisdiction: state.hasValidJurisdiction(),
    accountBasics: state.hasValidAccountBasics(),
    complete: state.isDataComplete(),
  }));
