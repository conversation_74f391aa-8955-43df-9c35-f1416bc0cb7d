'use client';

import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@kratex-tradetech/kratex-ui-library';

import BasicInformationTab from './basic-information/BasicInformationTab';
import EntityTypeAndKycTab from './entity-type-and-kyc/EntityTypeAndKycTab';
import SourcingIntentContainer from '../sourcing-intent-management/display-and-edit/SourcingIntentDisplayContainer';
import TeamMembersDisplayContainer from '../team-members-management/display-and-add/TeamMembersDisplayContainer';

export default function BuyerDetailTabs() {
  const [activeTab, setActiveTab] = useState('basic');

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div>
            <h1 className="text-2xl font-semibold text-gray-900">Profile Management</h1>
            <p className="text-gray-600">Manage your details and compliance status</p>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="basic" className="cursor-pointer">
            Basic Information
          </TabsTrigger>
          <TabsTrigger value="entity" className="cursor-pointer">
            Entity Type & KYC
          </TabsTrigger>
          <TabsTrigger value="sourcing" className="cursor-pointer">
            Sourcing Intent
          </TabsTrigger>
          <TabsTrigger value="members" className="cursor-pointer">
            Invite Members
          </TabsTrigger>
        </TabsList>

        <TabsContent value="basic" className="mt-6">
          <BasicInformationTab />
        </TabsContent>

        <TabsContent value="entity" className="mt-6">
          <EntityTypeAndKycTab />
        </TabsContent>

        <TabsContent value="sourcing" className="mt-6">
          <SourcingIntentContainer userType="buyer" showEditButton={true} />
        </TabsContent>

        <TabsContent value="members" className="mt-6">
          <TeamMembersDisplayContainer showEditButton={true} title="Team Members" />
        </TabsContent>
      </Tabs>
    </div>
  );
}
