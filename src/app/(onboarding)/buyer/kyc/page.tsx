'use client';
import GenericFormRenderer from '@/components/common/GenericFormRenderer';
import BuyingAgentIndia from '@/components/buyer/onboarding/kyc/buyingAgentIndia/buyingAgentIndia';
import GovernmentBodyIndia from '@/components/buyer/onboarding/kyc/governmentBody/governmentBody';
import IndividualIndia from '@/components/buyer/onboarding/kyc/individualIndia/IndividualIndia';
import NonProfitIndia from '@/components/buyer/onboarding/kyc/nonProfitIndia/nonProfitIndia';
import RegisteredCompanyIndia from '@/components/buyer/onboarding/kyc/registered-company-india/RegisteredCompanyIndia';
import SolePropriterIndia from '@/components/buyer/onboarding/kyc/soleProprietorIndia/SoleProprietorIndia';
import { useGetUserInfo } from '@/services/auth/useGetUserInfo';
import { LoaderCircle } from 'lucide-react';
import { useMemo } from 'react';

const KYC = () => {
  const {
    data: buyerInfoData,
    isLoading: isGetBuyerInfoLoading,
    isError: isGetBuyerInfoError,
  } = useGetUserInfo('buyer');

  const Form = useMemo(() => {
    const orgName = buyerInfoData?.organisation_type?.id;

    /*
      "results": [
        {
            "id": 1,
            "name": "Individual"
        },
        {
            "id": 2,
            "name": "Sole Proprietor"
        },
        {
            "id": 3,
            "name": "Registered Company"
        },
        {
            "id": 4,
            "name": "Government Body"
        },
        {
            "id": 5,
            "name": "Non-Profit Organization"
        },
        {
            "id": 6,
            "name": "Buying Agent"
        }
    ]
     */

    switch (orgName) {
      case 1: // Individual
        return <IndividualIndia />;

      case 2: // Sole Proprietor
        return <SolePropriterIndia />;

      case 3: // Registered Company
        return <RegisteredCompanyIndia />;

      case 4: // Government Body
        return <GovernmentBodyIndia />;

      case 5: // Non-Profit Organization
        return <NonProfitIndia />;

      case 6: // Buying Agent
        return <BuyingAgentIndia />;

      default:
        return (
          <GenericFormRenderer
            orgId={buyerInfoData?.organisation_type?.id || 0}
            orgName={buyerInfoData?.organisation_type?.name || ''}
          />
        );
    }
  }, [buyerInfoData?.organisation_type?.id, buyerInfoData?.organisation_type?.name]);

  if (isGetBuyerInfoError || !buyerInfoData?.organisation_type?.name)
    <pre>{JSON.stringify(buyerInfoData)}</pre>;

  if (isGetBuyerInfoLoading) return <LoaderCircle className="animate-spin" />;

  return Form;
};

export default KYC;
