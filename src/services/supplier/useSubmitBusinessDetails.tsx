import { UseMutationHookProps } from '@/types/api';
import { useMutation } from '@tanstack/react-query';
import axios, { AxiosError } from 'axios';
import { SubmitBusinessDetailsPayload, SubmitBusinessDetailsResponse } from './types';
import { apiUrls } from '@/constants/apiUrls';

const submitBusinessDetails = async (payload: SubmitBusinessDetailsPayload) => {
  const response = await axios.post(apiUrls.supplier.register, payload);
  return response.data;
};

export const useSubmitBusinessDetails = ({
  onSuccess,
  onError,
}: UseMutationHookProps<SubmitBusinessDetailsResponse, { detail: string; message: string }>) => {
  return useMutation<
    SubmitBusinessDetailsResponse,
    AxiosError<{ detail: string; message: string }>,
    SubmitBusinessDetailsPayload
  >({
    mutationFn: (payload: SubmitBusinessDetailsPayload) => submitBusinessDetails(payload),
    onSuccess,
    onError,
  });
};
