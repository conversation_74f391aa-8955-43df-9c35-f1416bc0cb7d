import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@kratex-tradetech/kratex-ui-library';

import InviteTeam from '../invite-team/InviteTeam';

interface Props {
  isOpen: boolean;
  onClose: () => void;
}

const AddTeamMembersModal: React.FC<Props> = ({ isOpen, onClose }) => {
  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="max-h-[90vh] max-w-4xl min-w-3xl overflow-auto">
        <DialogHeader>
          <DialogTitle>Add Team Members</DialogTitle>
        </DialogHeader>
        <InviteTeam />
      </DialogContent>
    </Dialog>
  );
};

export default AddTeamMembersModal;
