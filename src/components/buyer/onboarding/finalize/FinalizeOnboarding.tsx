'use client';

import {
  <PERSON>,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  Button,
  Separator,
} from '@kratex-tradetech/kratex-ui-library';
import { CheckCircle } from 'lucide-react';
import { useReviewOnboarding } from './useFinalize';
import { withBuyerRoute } from '@/utils/withRoleAccess';

// Component imports
import LoadingState from './LoadingState';
import EmptyState from './EmptyState';
import PersonalInfo from './PersonalInfoCard';
import OrganizationInfo from './OrganisationInfo';
import KycStatus from './KycStatus';
import JurisdictionCard from './JurisdictionCard';
import SourcingIntentDisplayContainer from '../../sourcing-intent-management/display-and-edit/SourcingIntentDisplayContainer';
import TeamMembersDisplayContainer from '../../team-members-management/display-and-add/TeamMembersDisplayContainer';

const ReviewOnboarding = () => {
  const { onboardingData, isLoading, isSubmitting, handleConfirmFinish } = useReviewOnboarding();
  if (isLoading) {
    return <LoadingState />;
  }

  if (!onboardingData) {
    return <EmptyState />;
  }

  return (
    <div className="min-h-screen p-6">
      <div className="mx-auto max-w-4xl">
        <Card>
          {/* Header */}
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5" />
              Review and Finalize
            </CardTitle>
            <CardDescription>
              Please review all the information you have provided before finalizing your account
              setup.
            </CardDescription>
          </CardHeader>

          {/* Content */}
          <CardContent className="space-y-6">
            <JurisdictionCard data={onboardingData.jurisdiction} />
            <OrganizationInfo data={onboardingData.organization_info} />
            <PersonalInfo data={onboardingData.accountbasics_info} />
            <KycStatus />

            <SourcingIntentDisplayContainer userType="buyer" showEditButton={true} />
            <TeamMembersDisplayContainer title="Team Members" showEditButton={false} />
            <Separator />

            {/* Confirm Button */}
            <div className="flex justify-center pt-4">
              <Button
                onClick={handleConfirmFinish}
                disabled={isSubmitting}
                size="lg"
                className="min-w-48"
              >
                {isSubmitting ? 'Creating Account...' : 'Confirm & Finish'}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default withBuyerRoute(ReviewOnboarding);
