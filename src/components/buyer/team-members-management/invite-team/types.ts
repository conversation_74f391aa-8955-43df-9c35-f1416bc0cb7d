export interface TeamMemberInvite {
  id: string;
  full_name: string;
  phone_number: string;
  invite_email: string;
  role: string;
}

export interface FormErrors {
  [key: string]: {
    full_name?: string;
    phone_number?: string;
    invite_email?: string;
    role?: string;
  };
}
export const seatRoleOptions = [
  { value: 'PROCUREMENT', label: 'Procurement' },
  { value: 'USER_MANAGER', label: 'User Manager' },
  { value: 'FINANCE', label: 'Finance' },
  { value: 'VIEW_ONLY', label: 'View Only' },
] as const;

export interface InvitedTeamResponse {
  data: Data[];
}

export interface Data {
  id: number;
  phone: string;
  email: string;
  user_type: number;
  invited_by: InvitedBy;
}

export interface InvitedBy {
  phone: string;
  email: string;
  phone_verified: boolean;
  email_verified: boolean;
  is_active: boolean;
  is_staff: boolean;
  user_type: number;
  role: string | null;
  kratos_id: string;
  id: number;
  onboarding_state: string;
  name: string;
  email_verification_flow_id: string | null;
}
