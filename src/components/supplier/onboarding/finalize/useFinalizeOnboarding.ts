'use client';

import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { OnboardingData } from './types';
import { useGetUserInfo } from '@/services/auth/useGetUserInfo';
import { useGetSourcingIntentInfo } from '@/services/sourcing-intent/useGetSourcingIntent';

function mapApiToOnboardingData(apiData: any): OnboardingData {
  return {
    personal_info: {
      full_name: apiData.buyer_details.display_name, // Not provided in API, leave blank or fetch from buyer_details if available
      email: apiData.email,
      phone_number: apiData.phone,
      country: apiData.jurisdiction?.country?.name || '',
      state: apiData.jurisdiction?.state?.name || '',
    },
    organization_info: {
      org_type: apiData.organisation_type?.name || '',
      org_name: apiData.buyer_details.display_name, // Not provided in API, leave blank or fetch from buyer_details if available
      registration_number: '', // Not provided in API, leave blank or fetch from buyer_details if available
      tax_id: '', // Not provided in API, leave blank or fetch from buyer_details if available
      business_address: '', // Not provided in API, leave blank or fetch from buyer_details if available
    },
    sourcing_intent: {
      sourcing_for: [],
      sourcing_from: [],
      use_case: '',
      product_categories: [],
      compliance_reqs: [],
      pref_shipping_term: '',
      target_lead_time: '',
    },
    team_members: [],
    kyc_status: 'PENDING', // Not provided in API, default to PENDING
  };
}

export const useFinalizeOnboarding = () => {
  const router = useRouter();
  const { data: buyerData, isLoading: isBuyerLoading } = useGetUserInfo('buyer');
  const { data: sourcingData, isLoading: isSourcingLoading } = useGetSourcingIntentInfo('buyer');

  function mapSourcingIntentApiToData(apiData: any) {
    return {
      sourcing_for: (apiData.sourcing_for || []).map((item: any) => item.name),
      sourcing_from: (apiData.sourcing_from || []).map((item: any) => item.name),
      use_case: apiData.use_case?.[0]?.value || '',
      use_case_enum: apiData.use_case?.[0]?.enum,
      product_categories: apiData.product_categories ? apiData.product_categories.split(',') : [],
      compliance_reqs: (apiData.compliance_reqs || []).map((item: any) => item.value),
      compliance_reqs_enum: (apiData.compliance_reqs || []).map((item: any) => item.enum),
      pref_shipping_term: apiData.shipping_term?.value || '',
      pref_shipping_term_enum: apiData.shipping_term?.enum,
      target_lead_time: apiData.lead_time?.value || '',
      target_lead_time_enum: apiData.lead_time?.enum,
    };
  }

  const onboardingData = buyerData
    ? {
        ...mapApiToOnboardingData(buyerData),
        sourcing_intent: sourcingData
          ? mapSourcingIntentApiToData(sourcingData)
          : {
              sourcing_for: [],
              sourcing_from: [],
              use_case: '',
              product_categories: [],
              compliance_reqs: [],
              pref_shipping_term: '',
              target_lead_time: '',
            },
      }
    : null;
  const isLoading = isBuyerLoading || isSourcingLoading;
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleConfirmFinish = async () => {
    setIsSubmitting(true);
    try {
      // Simulate account creation API call
      await new Promise((res) => setTimeout(res, 2000));
      router.push('/buyer/dashboard');
    } catch (e) {
      console.error(e);
      alert('Error creating account. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    onboardingData,
    isLoading,
    isSubmitting,
    handleConfirmFinish,
  };
};
