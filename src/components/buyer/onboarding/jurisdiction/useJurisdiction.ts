/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';

import { useGeoLookup } from '@/services/geolocation/useGeoLookup';
import { useCountries, useStates } from '@/services/geolocation/useGetJurisdiction';
import { newBuyerStore } from '@/store/newBuyer.store';
import { Country, State } from '@/types/newBuyer.types';
import { useRouter } from 'next/navigation';
import { useEffect, useRef, useState } from 'react';

// Optional: define the shape of the geo lookup response
interface GeoLookupData {
  country_code_iso3: string;
}

function isCountry(country: any): country is Country {
  return (
    typeof country?.iso2 === 'string' &&
    typeof country?.iso3 === 'string' &&
    typeof country?.name === 'string' &&
    typeof country?.emoji === 'string' &&
    typeof country?.phone_code === 'string'
  );
}

export const useJurisdiction = () => {
  const router = useRouter();
  const { setJurisdiction, newBuyerData } = newBuyerStore();

  const [selectedCountry, setSelectedCountry] = useState<Country | null>(
    newBuyerData?.country || null,
  );
  const [selectedState, setSelectedState] = useState<State | null>(newBuyerData?.state || null);

  const {
    data: countries = [],
    isLoading: loadingCountries,
    error: countriesError,
  } = useCountries();

  const {
    data: states = [],
    isLoading: loadingStates,
    error: statesError,
  } = useStates(selectedCountry?.iso2); // ISO2 for dependent states API

  const { data: geoData, refetch: geoRefetch, isSuccess: geoSuccess } = useGeoLookup();
  const geoLookupTriedRef = useRef(false);

  const fallbackToIndia = (): Country | null => {
    const india = countries.find((c) => c.alpha2 === 'IN');
    return isCountry(india) ? india : null;
  };

  // Auto select country based on IP geo
  useEffect(() => {
    if (
      !geoLookupTriedRef.current &&
      selectedCountry &&
      !loadingCountries &&
      countries.length > 0
    ) {
      geoLookupTriedRef.current = true;
      geoRefetch();
    }
  }, [loadingCountries, countries, selectedCountry, geoRefetch]);

  useEffect(() => {
    if (geoSuccess && geoData) {
      const geo = geoData as GeoLookupData;
      const match = countries.find((c) => c.iso3 === geo.country_code_iso3);
      setSelectedCountry(isCountry(match) ? match : fallbackToIndia());
    }
  }, [geoSuccess, geoData, countries]);

  useEffect(() => {
    if (!selectedCountry) return setSelectedState(null);

    const isValid = states.some((state) => state.id === selectedState?.id);
    if (!isValid) setSelectedState(null);
  }, [selectedCountry, states, selectedState]);

  const getStateLabel = (countryCode: string) => {
    switch (countryCode) {
      case 'IND':
        return 'State/Union Territory';
      case 'UAE':
        return 'Emirate';
      case 'AUS':
        return 'State/Territory';
      case 'USA':
        return 'State';
      default:
        return 'State/Province';
    }
  };

  const validateForm = () => !!(selectedCountry && selectedState);

  const onJurisdictionSubmit = () => {
    if (validateForm()) {
      setJurisdiction({
        country: selectedCountry!,
        state: selectedState!,
      });
      router.push('/buyer/org-type');
    }
  };

  const handleUICountrySelect = (uiCountry: any) => {
    if (!uiCountry?.alpha2) return;

    const transformedCountry: Country = {
      id: uiCountry.id,
      iso2: uiCountry.alpha2,
      name: uiCountry.name,
      iso3: uiCountry.alpha3,
      alpha2: uiCountry.alpha2,
      alpha3: uiCountry.alpha3,
      emoji: uiCountry.emoji ?? '',
      countryCallingCodes: uiCountry.countryCallingCodes,
      ioc: uiCountry.ioc,
    };

    setSelectedCountry(transformedCountry);
    setSelectedState(null);
  };

  const resetJurisdiction = () => {
    setSelectedCountry(null);
    setSelectedState(null);
  };

  return {
    countries,
    states,
    selectedCountry,
    selectedState,
    loadingCountries,
    loadingStates,
    countriesError,
    statesError,
    setSelectedCountry,
    setSelectedState,
    handleUICountrySelect,
    getStateLabel,
    validateForm,
    isValid: validateForm(),
    onJurisdictionSubmit,
    resetJurisdiction,
  };
};
