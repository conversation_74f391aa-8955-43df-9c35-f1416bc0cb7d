import {
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  CardHeader,
  CardTitle,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@kratex-tradetech/kratex-ui-library';
import { User } from 'lucide-react';
import React from 'react';

import { InvitedTeamResponse } from '../invite-team/types';

interface TeamMembersDisplayProps {
  data?: InvitedTeamResponse;
  isSuccess?: boolean;
  isLoading?: boolean;
  isError?: boolean;
  showEditButton?: boolean;
  title?: string;
  icon?: React.ReactNode;
  className?: string;
  variant?: 'card' | 'inline';
  onEdit?: () => void;
}

const TeamMembersDisplay = ({
  data,
  className,
  icon,
  onEdit,
  showEditButton,
  title,
}: TeamMembersDisplayProps) => {
  const teamMembers = data?.data || [];
  const teamMembersCount = data && data?.data.length > 0 ? `(${data?.data.length})` : '';
  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2 text-lg">
            {icon}
            <h3 className="mb-1 text-lg font-medium text-gray-900">
              {title} {teamMembersCount}
            </h3>
          </CardTitle>
          {showEditButton && onEdit && (
            <Button className="cursor-pointer" variant="outline" size="sm" onClick={onEdit}>
              <User className="mr-1 h-3 w-3" />
              Add Team Members
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <div className="rounded-lg border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Email Address</TableHead>
                <TableHead>Phone Number</TableHead>
                <TableHead>Invited By</TableHead>
                {/* <TableHead>Actions</TableHead> */}
              </TableRow>
            </TableHeader>

            <TableBody>
              {teamMembers?.map((contact) => (
                <TableRow key={contact.id}>
                  <TableCell className="text-blue-600">{contact.email}</TableCell>
                  <TableCell>{contact.phone}</TableCell>
                  <TableCell>{contact.invited_by?.email}</TableCell>
                  {/* <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button className="cursor-pointer" variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem className="cursor-pointer">Edit Contact</DropdownMenuItem>
                        <DropdownMenuItem className="cursor-pointer">
                          Resend Invitation
                        </DropdownMenuItem>
                        <DropdownMenuItem className="cursor-pointer text-red-600">
                          Remove Access
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell> */}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
};

export default TeamMembersDisplay;
