import AppConfigInitializer from '@/components/AppConfigInitializer';
import { Toaster } from '@/components/common/Toaster';
import QueryProvider from '@/providers/query-provider';
import type { Metadata } from 'next';
import { Geist, <PERSON>eist_Mono } from 'next/font/google';
import './globals.css';
import '@kratex-tradetech/kratex-ui-library/styles';
import GlobalPatch from '@/components/GlobalPatch';

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
  display: 'swap',
  preload: true,
});

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
  display: 'swap',
  preload: true,
});

export const metadata: Metadata = {
  title: 'Kratex Tradetech',
  description: 'Powering Construction Trade & Supply Chain',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${geistSans.variable} ${geistMono.variable} antialiased`}>
        <GlobalPatch />
        <QueryProvider>
          <AppConfigInitializer>{children}</AppConfigInitializer>
          <Toaster />
        </QueryProvider>
      </body>
    </html>
  );
}
