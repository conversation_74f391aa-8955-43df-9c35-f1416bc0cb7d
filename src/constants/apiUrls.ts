import { getKycPayload } from '@/services/kyc/useGetKycStatus';
import { UserRole } from '@/types/user.types';

const BASE = '/api/v1';
export const apiUrls = {
  files: {
    upload: `${BASE}/files/`,
  },
  aadhaar: {
    sendOtp: `${BASE}/kyc/aadhaar/otp`,
    verifyOtp: `${BASE}/kyc/aadhaar/otp/verify`,
  },
  kyc: `${BASE}/kyc/`,
  panVerify: `${BASE}/kyc/pan/verify`,
  skip: `${BASE}/users/states/`, // used to update backend state of user if he chooses to skip an step in onboarding
  users: {
    // keyword(formio kyc form schema's keyword) might be updated later to be set from backend itself
    getKycStatusData: ({ userType, keyword }: getKycPayload) =>
      `${BASE}/users/${userType}/kyc/${keyword}/`,
    info: (userType: UserRole) => `${BASE}/users/${userType}/info/`,
    me: () => `${BASE}/users/me`,
    invite: (userType: UserRole) => `${BASE}/users/${userType}/invite`,
    invitedTeamMembers: `${BASE}/users/invites`,
    completeOnboarding: (userType: UserRole) => `${BASE}/users/${userType}/onboarding/complete/`,
  },
  supplier: {
    register: `${BASE}/users/supplier/registration/`,
  },
};
