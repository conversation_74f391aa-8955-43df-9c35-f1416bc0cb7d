import { useConfigStore } from '@/store/useAppConfig.store';
import axios, { AxiosResponse, InternalAxiosRequestConfig } from 'axios';
import { handleApiError } from './error-handler';
import { clearTokenFromCookie, getTokenFromCookie } from './cookies';

let configPromise: Promise<void> | null = null;

const waitForConfig = (): Promise<void> => {
  const state = useConfigStore.getState();

  // If config is already loaded, return immediately
  if (state.isConfigLoaded) {
    return Promise.resolve();
  }

  // If there's an error, reject immediately
  if (state.error) {
    return Promise.reject(new Error(`Config loading failed: ${state.error}`));
  }

  // If already waiting for config, return existing promise
  if (configPromise) {
    return configPromise;
  }

  // Create new promise to wait for config
  configPromise = new Promise<void>((resolve, reject) => {
    const unsubscribe = useConfigStore.subscribe((state) => {
      unsubscribe();
      configPromise = null;
      if (state.isConfigLoaded) {
        resolve();
      } else if (state.error) {
        reject(new Error(`Config loading failed: ${state.error}`));
      }
    });

    // Check again after subscribing in case state changed
    const currentState = useConfigStore.getState();
    unsubscribe();
    configPromise = null;

    if (currentState.isConfigLoaded) {
      resolve();
    } else if (currentState.error) {
      reject(new Error(`Config loading failed: ${currentState.error}`));
    }
  });

  return configPromise;
};

// Create axios instance without baseURL initially
const axiosInstance = axios.create({
  timeout: 30000, // 30 seconds timeout
});

axiosInstance.interceptors.request.use(
  async (config: InternalAxiosRequestConfig) => {
    // Wait for config to be loaded
    await waitForConfig();

    const appConfig = useConfigStore.getState().config;

    // Set base URL if not already set and config is available
    if (appConfig?.API_BASE_URL && !config.baseURL && !config.url?.startsWith('http')) {
      // Only set baseURL for relative URLs
      config.baseURL = appConfig.API_BASE_URL;
    }

    // Add authorization token if available
    const authToken = getTokenFromCookie();
    if (authToken) {
      config.headers.Authorization = `Bearer ${authToken}`;
    }

    return config;
  },
  (error) => Promise.reject(error),
);

axiosInstance.interceptors.response.use(
  (response: AxiosResponse) => response,
  (error) => {
    const status = error?.response?.status;

    // TODO: this is temporarily made 403, must be changed back to 401 after backend is updated.
    if (status === 403 && typeof window !== 'undefined') {
      clearTokenFromCookie();
      // also clear zustand states
      localStorage.clear();
      window.location.href = '/login-signup';
    }

    return Promise.reject(
      handleApiError(
        error,
        error.code === 'ERR_NETWORK' ? 'Network error occurred' : 'An unexpected error occurred',
      ),
    );
  },
);

export default axiosInstance;
