import apiClient from '@/lib/api-client';
import { handleApiError } from '@/lib/error-handler';
import { useQuery } from '@tanstack/react-query';

async function getSourcingIntentInfo(userRole: string): Promise<any> {
  try {
    const response = await apiClient.get<any>(`api/v1/users/${userRole}/sourcing-intent/info/`);
    return response.data!;
  } catch (error) {
    throw handleApiError(error, 'Failed to fetch sourcing intent info.');
  }
}

export const useGetSourcingIntentInfo = (userRole: string) => {
  return useQuery({
    queryKey: ['buyer', 'sourcing-intent', 'info'],
    queryFn: () => getSourcingIntentInfo(userRole),
  });
};
