/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  AccountBasics,
  Country,
  Jurisdiction,
  NewSupplierData,
  OrgType,
  PhoneNo,
} from '@/types/newSupplier.types';
import { RoleEnum } from '@/types/user.types';
import { createPersistedStore } from '@/utils/zustand/createPersistedStore';

interface NewSupplierStore {
  newSupplierData: NewSupplierData;
  setNewSupplierData: (data: NewSupplierData) => void;
  updateNewSupplierData: (data: Partial<NewSupplierData>) => void;
  clearNewSupplierData: () => void;
  resetToDefault: () => void;

  // Individual field setters for convenience
  setPhoneNo: (phoneNo: PhoneNo) => void;
  setRole: (role: RoleEnum) => void;
  setJurisdiction: (jurisdiction: Jurisdiction) => void;
  setOrgType: (orgType: OrgType[]) => void;
  setAccountBasics: (accountBasics: AccountBasics) => void;
  setEmailVerified: (verified: boolean) => void;

  // Computed getters for type safety
  getNewSupplierData: () => NewSupplierData;
  isDataComplete: () => boolean;
  hasValidPhoneNo: () => boolean;
  hasValidRole: () => boolean;
  hasValidOrgType: () => boolean;
  hasValidJurisdiction: () => boolean;
  hasValidAccountBasics: () => boolean;
}

// Type-safe creation of default data
const createDefaultNewSupplierData = (): NewSupplierData => ({
  phoneNo: '' as PhoneNo,
  jurisdiction: {
    countryOfOrigin: {
      id: 0,
      name: '',
      emoji: '',
      alpha2: '',
      alpha3: '',
      iso2: '',
      iso3: '',
      countryCallingCodes: [],
      ioc: '',
    } as Country,
    countriesToServe: [] as Country[],
  } as Jurisdiction,
  orgType: [] as OrgType[],
  accountBasics: {} as AccountBasics,
  isEmailVerified: false,
  role: 3 as RoleEnum,
});

const defaultNewSupplierData: NewSupplierData = createDefaultNewSupplierData();

// Enhanced type guard functions with better type safety
const isValidPhoneNo = (phoneNo: PhoneNo): phoneNo is NonNullable<PhoneNo> => {
  return typeof phoneNo === 'string' && phoneNo.trim().length > 0;
};

const isValidCountry = (country: Country): country is Required<Country> => {
  return (
    typeof country === 'object' &&
    country !== null &&
    typeof country.id === 'number' &&
    country.id > 0 &&
    typeof country.name === 'string' &&
    country.name.trim().length > 0
  );
};

const isValidRole = (role: RoleEnum): role is Required<RoleEnum> => {
  return role !== null && typeof role === 'number' && role > 0;
};

const isValidOrgType = (orgTypes: OrgType[]): orgTypes is Required<OrgType[]> => {
  return (
    Array.isArray(orgTypes) &&
    orgTypes.length > 0 &&
    orgTypes.every(
      (org) =>
        typeof org === 'object' &&
        org !== null &&
        typeof org.id === 'number' &&
        org.id > 0 &&
        typeof org.name === 'string' &&
        org.name.trim().length > 0,
    )
  );
};

const isValidJurisdiction = (
  jurisdiction: Jurisdiction,
): jurisdiction is Required<Jurisdiction> => {
  return (
    typeof jurisdiction === 'object' &&
    jurisdiction !== null &&
    isValidCountry(jurisdiction.countryOfOrigin) &&
    Array.isArray(jurisdiction.countriesToServe) &&
    jurisdiction.countriesToServe.length > 0 &&
    jurisdiction.countriesToServe.every((country) => isValidCountry(country))
  );
};

const isValidAccountBasics = (
  accountBasics: AccountBasics,
): accountBasics is Required<AccountBasics> => {
  return (
    typeof accountBasics === 'object' &&
    accountBasics !== null &&
    Object.keys(accountBasics).length > 0
  );
};

// Type-safe deep merge utility
const deepMerge = <T extends Record<string, any>>(target: T, source: Partial<T>): T => {
  const result = { ...target };

  for (const key in source) {
    if (source.hasOwnProperty(key)) {
      const sourceValue = source[key];
      const targetValue = result[key];

      if (
        sourceValue !== null &&
        typeof sourceValue === 'object' &&
        !Array.isArray(sourceValue) &&
        targetValue !== null &&
        typeof targetValue === 'object' &&
        !Array.isArray(targetValue)
      ) {
        result[key] = deepMerge(targetValue, sourceValue);
      } else if (sourceValue !== undefined) {
        result[key] = sourceValue as T[Extract<keyof T, string>];
      }
    }
  }

  return result;
};

// Helper function to safely merge partial data with enhanced type safety
const safeMergeNewSupplierData = (
  current: NewSupplierData,
  updates: Partial<NewSupplierData>,
): NewSupplierData => {
  // Use deep merge for better type safety
  const merged = deepMerge(current, updates);

  // Ensure jurisdiction consistency
  if (updates.jurisdiction) {
    merged.jurisdiction = {
      countryOfOrigin: updates.jurisdiction.countryOfOrigin
        ? deepMerge(current.jurisdiction.countryOfOrigin, updates.jurisdiction.countryOfOrigin)
        : current.jurisdiction.countryOfOrigin,
      countriesToServe: updates.jurisdiction.countriesToServe
        ? [...updates.jurisdiction.countriesToServe]
        : current.jurisdiction.countriesToServe,
    };
  }

  return merged;
};

// Helper function to create a complete NewSupplierData from partial data
const createCompleteNewSupplierData = (partialData: Partial<NewSupplierData>): NewSupplierData => {
  return safeMergeNewSupplierData(createDefaultNewSupplierData(), partialData);
};

// Type-safe serialization helpers
const serializeState = (state: Pick<NewSupplierStore, 'newSupplierData'>): string => {
  try {
    return JSON.stringify(state);
  } catch (error) {
    console.error('Failed to serialize newSupplierStore state:', error);
    return JSON.stringify({ newSupplierData: defaultNewSupplierData });
  }
};

const deserializeState = (str: string): Pick<NewSupplierStore, 'newSupplierData'> => {
  try {
    const parsed: unknown = JSON.parse(str);

    // Type guard for parsed data
    if (parsed && typeof parsed === 'object' && parsed !== null && 'newSupplierData' in parsed) {
      const typedParsed = parsed as { newSupplierData: Partial<NewSupplierData> };
      return {
        newSupplierData: safeMergeNewSupplierData(
          defaultNewSupplierData,
          typedParsed.newSupplierData,
        ),
      };
    }

    return { newSupplierData: defaultNewSupplierData };
  } catch (error) {
    console.error('Failed to deserialize newSupplierStore state:', error);
    return { newSupplierData: defaultNewSupplierData };
  }
};

// Migration function with proper typing
const migrateState = (
  persistedState: unknown,
  version: number,
): Pick<NewSupplierStore, 'newSupplierData'> => {
  if (version === 0) {
    // Handle migration from version 0 to 1
    if (
      persistedState &&
      typeof persistedState === 'object' &&
      persistedState !== null &&
      'newSupplierData' in persistedState
    ) {
      const typedState = persistedState as { newSupplierData?: Partial<NewSupplierData> };
      return {
        newSupplierData: safeMergeNewSupplierData(
          defaultNewSupplierData,
          typedState.newSupplierData || {},
        ),
      };
    }
  }

  // Fallback for unknown versions or invalid data
  if (
    persistedState &&
    typeof persistedState === 'object' &&
    persistedState !== null &&
    'newSupplierData' in persistedState
  ) {
    return persistedState as Pick<NewSupplierStore, 'newSupplierData'>;
  }

  return { newSupplierData: defaultNewSupplierData };
};

export const newSupplierStore = createPersistedStore<NewSupplierStore>(
  'new-supplier-store',
  (set, get) => ({
    newSupplierData: defaultNewSupplierData,

    setNewSupplierData: (data: NewSupplierData) => {
      set({ newSupplierData: data });
    },

    updateNewSupplierData: (data: Partial<NewSupplierData>) => {
      set((state: { newSupplierData: NewSupplierData }) => ({
        newSupplierData: safeMergeNewSupplierData(state.newSupplierData, data),
      }));
    },

    clearNewSupplierData: () => {
      set({ newSupplierData: createDefaultNewSupplierData() });
    },

    resetToDefault: () => {
      set({ newSupplierData: defaultNewSupplierData });
    },

    // Individual field setters with enhanced type safety
    setPhoneNo: (phoneNo: PhoneNo) => {
      set((state: { newSupplierData: any }) => ({
        newSupplierData: {
          ...state.newSupplierData,
          phoneNo,
        },
      }));
    },

    setRole: (role: RoleEnum) => {
      if (!isValidRole(role)) {
        console.warn('Invalid role provided to setRole:', role);
        return;
      }
      set((state: { newSupplierData: any }) => ({
        newSupplierData: {
          ...state.newSupplierData,
          role,
        },
      }));
    },

    setEmailVerified: (verified: boolean) =>
      set(
        (state: {
          newSupplierData: {
            isEmailVerified: boolean;
          };
        }) => ({
          newSupplierData: {
            ...state.newSupplierData,
            isEmailVerified: verified,
          },
        }),
      ),

    setJurisdiction: (jurisdiction: Jurisdiction) => {
      set((state: { newSupplierData: any }) => ({
        newSupplierData: {
          ...state.newSupplierData,
          jurisdiction: {
            countryOfOrigin: { ...jurisdiction.countryOfOrigin },
            countriesToServe: [...jurisdiction.countriesToServe],
          },
        },
      }));
    },

    setOrgType: (orgTypes: OrgType[]) => {
      set((state: { newSupplierData: any }) => ({
        newSupplierData: {
          ...state.newSupplierData,
          orgType: orgTypes,
        },
      }));
    },

    setAccountBasics: (accountBasics: AccountBasics) => {
      set((state: { newSupplierData: { accountBasics: any } }) => ({
        newSupplierData: {
          ...state.newSupplierData,
          accountBasics: {
            ...state.newSupplierData.accountBasics,
            ...accountBasics,
          },
        },
      }));
    },

    // Enhanced computed getters for type safety
    getNewSupplierData: (): NewSupplierData => {
      return get().newSupplierData;
    },

    isDataComplete: (): boolean => {
      const data = get().newSupplierData;
      return (
        isValidPhoneNo(data.phoneNo) &&
        isValidRole(data.role) &&
        isValidOrgType(data.orgType) &&
        isValidJurisdiction(data.jurisdiction) &&
        isValidAccountBasics(data.accountBasics)
      );
    },

    hasValidPhoneNo: (): boolean => {
      return isValidPhoneNo(get().newSupplierData.phoneNo);
    },

    hasValidRole: (): boolean => {
      return isValidRole(get().newSupplierData.role);
    },

    hasValidOrgType: (): boolean => {
      return isValidOrgType(get().newSupplierData.orgType);
    },

    hasValidJurisdiction: (): boolean => {
      return isValidJurisdiction(get().newSupplierData.jurisdiction);
    },

    hasValidAccountBasics: (): boolean => {
      return isValidAccountBasics(get().newSupplierData.accountBasics);
    },
  }),
);

// Export typed hooks for better developer experience with enhanced type safety
export const useNewSupplierData = () => newSupplierStore((state) => state.newSupplierData);

export const useNewSupplierActions = () =>
  newSupplierStore((state) => ({
    setNewSupplierData: state.setNewSupplierData,
    updateNewSupplierData: state.updateNewSupplierData,
    clearNewSupplierData: state.clearNewSupplierData,
    resetToDefault: state.resetToDefault,
    setPhoneNo: state.setPhoneNo,
    setRole: state.setRole,
    setJurisdiction: state.setJurisdiction,
    setOrgType: state.setOrgType,
    setAccountBasics: state.setAccountBasics,
    setEmailVerified: state.setEmailVerified,
  }));

export const useNewSupplierValidation = () =>
  newSupplierStore((state) => ({
    isDataComplete: state.isDataComplete,
    hasValidPhoneNo: state.hasValidPhoneNo,
    hasValidRole: state.hasValidRole,
    hasValidOrgType: state.hasValidOrgType,
    hasValidJurisdiction: state.hasValidJurisdiction,
    hasValidAccountBasics: state.hasValidAccountBasics,
  }));

// Type-safe selectors for specific data pieces
export const useNewSupplierPhoneNo = () =>
  newSupplierStore((state) => state.newSupplierData.phoneNo);
export const useNewSupplierRole = () => newSupplierStore((state) => state.newSupplierData.role);
export const useNewSupplierOrgType = () =>
  newSupplierStore((state) => state.newSupplierData.orgType);
export const useNewSupplierJurisdiction = () =>
  newSupplierStore((state) => state.newSupplierData.jurisdiction);
export const useNewSupplierAccountBasics = () =>
  newSupplierStore((state) => state.newSupplierData.accountBasics);

// Jurisdiction-specific selectors
export const useNewSupplierCountryOfOrigin = () =>
  newSupplierStore((state) => state.newSupplierData.jurisdiction.countryOfOrigin);
export const useNewSupplierCountriesToServe = () =>
  newSupplierStore((state) => state.newSupplierData.jurisdiction.countriesToServe);

// Type-safe computed selectors
export const useIsNewSupplierDataComplete = () =>
  newSupplierStore((state) => state.isDataComplete());
export const useNewSupplierValidationStatus = () =>
  newSupplierStore((state) => ({
    phoneNo: state.hasValidPhoneNo(),
    role: state.hasValidRole(),
    orgType: state.hasValidOrgType(),
    jurisdiction: state.hasValidJurisdiction(),
    accountBasics: state.hasValidAccountBasics(),
    complete: state.isDataComplete(),
  }));
