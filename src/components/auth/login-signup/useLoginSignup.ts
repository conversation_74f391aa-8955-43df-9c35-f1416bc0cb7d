import { useOnboardingStatusRedirect } from '@/hooks/useOnboardingRedirect';
import { setTokenInCookie } from '@/lib/cookies';
import { useExchangeToken } from '@/services/auth/useExchangeToken';
import { useRequestOTP } from '@/services/auth/useRequestOTP';
import { useVerifyOTP } from '@/services/auth/useVerifyOTP';
import { newBuyerStore } from '@/store/newBuyer.store';
import { newSupplierStore } from '@/store/newSupplier.store';
import { userStore } from '@/store/user.store';
import { OTPResponse } from '@/types/otp.types';
import { RoleEnum } from '@/types/user.types';
import { AxiosError } from 'axios';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { toast } from 'sonner';

const ROLES = [
  { id: 2, name: 'Buyer' },
  { id: 3, name: 'Supplier' },
];

export const useLoginSignup = () => {
  const router = useRouter();
  const { setPhoneNo: setNewBuyerPhoneNo, setRole: setNewBuyerRole } = newBuyerStore();

  const { setPhoneNo: setSupplierPhoneNo, setRole: setSupplierRole } = newSupplierStore();

  const { user, setUser } = userStore();

  // for getting the current onboarding stage of the user
  const { refetch: fetchUser } = useOnboardingStatusRedirect();

  const [phoneNumber, setPhoneNumber] = useState<string>('');

  const [role, setRoles] = useState<number>(ROLES[0].id);

  // TODO: this should probably be refactored properly into the loading, error and success states from mutation/query hooks
  const [loading, setLoading] = useState(false);
  const [otpSent, setOtpSent] = useState(false);
  const [otp, setOtp] = useState('');
  const [flowId, setFlowId] = useState<string>('');
  const OTP_LENGTH = 6;

  const { mutateAsync: requestOTP } = useRequestOTP({
    onSuccess: handlePhoneSubmitSuccess,
    onError: handlePhoneSubmitError,
    onSettled: handlePhoneResendSubmitSettled,
  });

  const { mutateAsync: resendOTP } = useRequestOTP({
    onSuccess: handleResendOTPSuccess,
    onError: handleResendOTPError,
    onSettled: handlePhoneResendSubmitSettled,
  });

  const { mutateAsync: verifyOTP } = useVerifyOTP();
  const { mutateAsync: exchangeJWT } = useExchangeToken();

  const handlePhoneSubmit = async (e?: React.FormEvent) => {
    e?.preventDefault();
    setLoading(true);
    await requestOTP({ phone_number: phoneNumber });
  };

  function handlePhoneSubmitSuccess(data: OTPResponse) {
    const { flow_id, message } = data;
    setFlowId(flow_id);
    setOtpSent(true);
    toast.success(`${message}`);
  }

  function handlePhoneSubmitError(error: AxiosError<{ message: string }>) {
    toast.error(
      typeof error === 'object' && error !== null && 'message' in error
        ? (error as { message?: string }).message || 'Failed to send OTP'
        : 'Failed to send OTP',
    );
  }

  // common onSettled function for both OTP send and Resend API calls
  function handlePhoneResendSubmitSettled() {
    setLoading(false);
  }

  const handleOtpSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (otp.length !== OTP_LENGTH || !flowId) return;

    setLoading(true);
    try {
      const { code, state } = await verifyOTP({
        phone_number: phoneNumber,
        otp,
        flow_id: flowId,
      });

      const { id_token, is_registered } = await exchangeJWT({
        code,
        state,
      });

      setTokenInCookie(id_token);
      setUser({ ...user, isRegistered: is_registered });

      // calling the user/me endpoint for getting the onboarding state
      fetchUser();

      if (!is_registered) {
        // TODO: refactor this after stores are merged into a single store for onboarding
        if (role === RoleEnum.BUYER) {
          setNewBuyerPhoneNo(phoneNumber);
          setNewBuyerRole(role);
        } else {
          setSupplierPhoneNo(phoneNumber);
          setSupplierRole(role);
        }
        const next = role === RoleEnum.BUYER ? '/buyer/jurisdiction' : '/supplier/jurisdiction';
        router.push(next);
      }
    } catch (error) {
      toast.error(
        typeof error === 'object' && error !== null && 'message' in error
          ? (error as { message?: string }).message || 'OTP verification failed'
          : 'OTP verification failed',
      );
    } finally {
      setLoading(false);
    }
  };

  const handleResendOTP = async () => {
    setLoading(true);
    await resendOTP({ phone_number: phoneNumber });
  };

  function handleResendOTPSuccess(data: OTPResponse) {
    const { flow_id, message } = data;
    setFlowId(flow_id);
    setOtpSent(true);
    toast.success(`${message}`);
  }

  function handleResendOTPError(error: AxiosError<{ message: string }>) {
    toast.error(
      typeof error === 'object' && error !== null && 'message' in error
        ? (error as { message?: string }).message || 'Failed to resend OTP'
        : 'Failed to resend OTP',
    );
  }

  const handleChangePhone = () => {
    setOtpSent(false);
    setOtp('');
  };

  return {
    phoneNumber,
    setPhoneNumber,
    role,
    setRoles,
    loading,
    otpSent,
    otp,
    setOtp,
    user,
    handlePhoneSubmit,
    handleOtpSubmit,
    handleChangePhone,
    handleResendOTP,
    ROLES,
  };
};
