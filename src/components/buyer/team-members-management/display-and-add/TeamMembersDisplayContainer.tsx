'use client';

import React from 'react';

import TeamMembersDisplay from './TeamMembersDisplay';
import useTeamMembersManagement from './useTeamMembersManagement';
import AddTeamMembersModal from './AddTeamMembersModal';

interface TeamMembersDisplayContainerProps {
  userType?: 'buyer' | 'seller';
  showEditButton?: boolean;
  title?: string;
  icon?: React.ReactNode;
  className?: string;
  variant?: 'card' | 'inline';
  onUpdateSuccess?: () => void;
  onUpdateError?: (error: any) => void;
}

const TeamMembersDisplayContainer: React.FC<TeamMembersDisplayContainerProps> = ({
  showEditButton = true,
  title,
  icon,
  className,
  variant = 'card',
}) => {
  const { displayData, isLoading, isModalOpen, openModal, closeModal } = useTeamMembersManagement();
  if (isLoading) {
    return (
      <div className="animate-pulse">
        <div className="h-48 rounded-lg bg-gray-200"></div>
      </div>
    );
  }

  if (!displayData) {
    return (
      <div className="py-8 text-center">
        <p className="text-gray-500">No team members found</p>
      </div>
    );
  }

  return (
    <>
      <TeamMembersDisplay
        data={displayData}
        onEdit={showEditButton ? openModal : undefined}
        showEditButton={showEditButton}
        title={title}
        icon={icon}
        className={className}
        variant={variant}
      />

      {showEditButton && <AddTeamMembersModal isOpen={isModalOpen} onClose={closeModal} />}
    </>
  );
};

export default TeamMembersDisplayContainer;
