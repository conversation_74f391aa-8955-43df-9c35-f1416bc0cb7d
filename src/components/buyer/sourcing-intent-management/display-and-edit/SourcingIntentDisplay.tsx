'use client';

import React from 'react';
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  Button,
  Badge,
  Label,
} from '@kratex-tradetech/kratex-ui-library';
import { Edit, Globe } from 'lucide-react';
import { SourcingIntentData } from './types';

interface SourcingIntentDisplayProps {
  data: SourcingIntentData;
  onEdit?: () => void;
  showEditButton?: boolean;
  title?: string;
  icon?: React.ReactNode;
  className?: string;
  variant?: 'card' | 'inline';
}

const SourcingIntentDisplay: React.FC<SourcingIntentDisplayProps> = ({
  data,
  onEdit,
  showEditButton = true,
  title = 'Sourcing Intent',
  icon = <Globe className="h-4 w-4" />,
  className = '',
  variant = 'card',
}) => {
  const renderArrayField = (items: string[], enums?: number[]) => {
    if (!items || items.length === 0) {
      return <p className="text-sm text-gray-500">None specified</p>;
    }
    return (
      <div className="flex flex-wrap gap-1">
        {items.map((item, index) => (
          <Badge key={index} variant="outline" className="text-sm">
            {item}
            {enums && enums[index] !== undefined ? ` (enum: ${enums[index]})` : ''}
          </Badge>
        ))}
      </div>
    );
  };

  const renderField = (label: string, content: React.ReactNode) => (
    <div className="space-y-2">
      <Label className="text-gray-600">{label}</Label>
      {content}
    </div>
  );

  const renderContent = () => (
    <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
      {renderField('Sourcing For', renderArrayField(data.sourcing_for))}
      {renderField('Sourcing From', renderArrayField(data.sourcing_from))}
      {renderField('Use Case', renderArrayField(data.use_case))}
      {renderField(
        'Shipping Term',
        <p className="text-sm">{data.pref_shipping_term || 'Not specified'}</p>,
      )}
      {renderField(
        'Lead Time',
        <p className="text-sm">{data.target_lead_time || 'Not specified'}</p>,
      )}
      {renderField('Product Categories', renderArrayField(data.product_categories))}
      {renderField('Compliance Requirements', renderArrayField(data.compliance_reqs))}
    </div>
  );

  if (variant === 'inline') {
    return (
      <div className={`space-y-4 ${className}`}>
        {showEditButton && onEdit && (
          <div className="flex items-center justify-between">
            <h3 className="flex items-center gap-2 text-lg font-semibold">
              {icon}
              {title}
            </h3>
            <Button variant="outline" size="sm" onClick={onEdit}>
              <Edit className="mr-1 h-3 w-3" />
              Edit
            </Button>
          </div>
        )}
        {renderContent()}
      </div>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2 text-lg">
            {icon}
            <h3 className="mb-1 text-lg font-medium text-gray-900">{title}</h3>
          </CardTitle>
          {showEditButton && onEdit && (
            <Button className="cursor-pointer" variant="outline" size="sm" onClick={onEdit}>
              <Edit className="mr-1 h-3 w-3" />
              Edit
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent>{renderContent()}</CardContent>
    </Card>
  );
};

export default SourcingIntentDisplay;
