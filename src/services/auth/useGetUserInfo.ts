import { apiUrls } from '@/constants/apiUrls';
import apiClient from '@/lib/api-client';
import { handleApiError } from '@/lib/error-handler';
import { FinalizeInfoResponse } from '@/types/finalizeResponse.types';
import { UserRole } from '@/types/user.types';
import { useQuery } from '@tanstack/react-query';

async function getUserInfo(userRole: UserRole) {
  try {
    const response = await apiClient.get<FinalizeInfoResponse>(apiUrls.users.info(userRole));
    return response.data!;
  } catch (error) {
    throw handleApiError(error, `Failed to fetch ${userRole} info.`);
  }
}

export const useGetUserInfo = (userRole: UserRole) => {
  return useQuery({
    queryKey: [userRole, 'info'],
    queryFn: () => getUserInfo(userRole),
  });
};
